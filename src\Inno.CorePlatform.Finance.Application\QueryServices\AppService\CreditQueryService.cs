using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using OfficeOpenXml;
using System.Linq;
using System.Linq.Expressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 应收查询
    /// </summary>
    public class CreditQueryService : QueryAppService, ICreditQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IPCApiClient _pCApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly ISellApiClient _sellApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IRecognizeReceiveAppService _recognizeReceiveAppService;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IPurchaseExcuteApiClient _purchaseExcuteApiClient;
        public CreditQueryService(IAppServiceContextAccessor? contextAccessor,
            IBDSApiClient iBDSApiClient,
            IPCApiClient pCApiClient,
            FinanceDbContext db,
            IInvoiceQueryService invoiceQueryService,
            ISellApiClient sellApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IInventoryApiClient inventoryApiClient,
            IPurchaseExcuteApiClient purchaseExcuteApiClient,
            IRecognizeReceiveAppService recognizeReceiveAppService,
        IUnitOfWork unitOfWork)
            : base(contextAccessor)
        {
            this._db = db;
            this._unitOfWork = unitOfWork;
            this._invoiceQueryService = invoiceQueryService;
            this._pCApiClient = pCApiClient;
            this._appServiceContextAccessor = contextAccessor;
            this._bDSApiClient = iBDSApiClient;
            this._sellApiClient = sellApiClient;
            this._sellApiClient = sellApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._recognizeReceiveAppService = recognizeReceiveAppService;
            _purchaseExcuteApiClient = purchaseExcuteApiClient;
        }
        /// <summary>
        /// 查询销项发票
        /// </summary>
        /// <param name="input"></param>
        /// <param name="isExport"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceCreditQueryListOutput>, int)> GetInvoiceCreditListAsync(InvoiceCreditQueryInput input, bool isExport = false)
        {
            #region old
            //var Query = from ic in _db.InvoiceCredits
            //            join c in _db.Credits on ic.CreditId equals c.Id into creditGroup
            //            from c in creditGroup.DefaultIfEmpty()
            //            join sid in _db.CustomizeInvoiceSubDetails on c.BillCode equals sid.CreditBillCode into subDetailGroup
            //            from sid in subDetailGroup.DefaultIfEmpty()
            //            join did in _db.CustomizeInvoiceDetail on sid.CustomizeInvoiceDetailId equals did.Id into detailGroup
            //            from did in detailGroup.DefaultIfEmpty()
            //            join item in _db.CustomizeInvoiceItem on did.CustomizeInvoiceItemId equals item.Id into itemGroup
            //            from item in itemGroup.DefaultIfEmpty()
            //            select new InvoiceCreditQueryListOutput
            //            {
            //                InvoiceNo = ic.InvoiceNo,
            //                InvoiceCode = ic.InvoiceCode,
            //                Type = ic.Type,
            //                InvoiceTime = ic.InvoiceTime,
            //                CreatedTime = ic.CreatedTime,
            //                CreatedBy = ic.CreatedBy,

            //                BusinessDeptFullName = c.BusinessDeptFullName,
            //                BusinessDeptFullPath = c.BusinessDeptFullPath,
            //                BusinessDeptId = c.BusinessDeptId,
            //                CustomerId = c.CustomerId,
            //                CustomerName = c.CustomerName,
            //                CompanyId = c.CompanyId,
            //                CompanyName = c.CompanyName,
            //                BillCode = c.BillCode,
            //                OrderNo = c.OrderNo,
            //                RelateCode = c.RelateCode,
            //                ServiceId = c.ServiceId,
            //                ServiceName = c.ServiceName,

            //                ProductName = did.ProductName,
            //                ProductNo = did.ProductNo,
            //                Specification = did.Specification,
            //                Quantity = did.Quantity,
            //                PackUnit = did.PackUnit,
            //                Price = did.Price,
            //                Value = did.Value,
            //                TaxAmount = did.TaxAmount,
            //                TaxRate = did.TaxRate,


            //                Code = item.Code

            //            };

            #endregion

            var Query = from output in _db.OutputInvoices
                        from item in _db.CustomizeInvoiceItem
                            .Where(x => x.Code == output.CustomizeInvoiceCode)
                            .DefaultIfEmpty()
                        from ic in _db.Invoices
                            .Where(x => x.InvoiceNo == output.InvoiceNo &&
                                       x.InvoiceCode == output.InvoiceCode &&
                                       x.CustomizeInvoiceCode == output.CustomizeInvoiceCode)
                            .DefaultIfEmpty()
                        select new InvoiceCreditQueryListOutput
                        {
                            InvoiceNo = output.InvoiceNo,
                            InvoiceCode = output.InvoiceCode,
                            Type = output.Type,
                            InvoiceTime = output.InvoiceTime,
                            CreatedTime = output.CreatedTime,
                            CreatedBy = output.CreatedBy,
                            BusinessDeptFullName = ic.BusinessDeptFullName,
                            BusinessDeptFullPath = ic.BusinessDeptFullPath,
                            BusinessDeptId = ic.BusinessDeptId,
                            CustomerId = ic.CustomerId,
                            CustomerName = ic.CustomerName,
                            CompanyId = ic.CompanyId,
                            CompanyName = ic.CompanyName,
                            BillCode = output.CreditNo,
                            HospitalName = ic.HospitalName,
                            HospitalId = ic.HospitalId,
                            ProductName = output.ProductName,
                            ProductNo = output.ProductNo,
                            Specification = "",
                            Quantity = output.Quantity,
                            PackUnit = output.Unit,
                            Price = output.UnitPrice,
                            TaxAmount = output.TaxAmount,
                            NoTaxAmountPrice = output.UnitPriceOfNoTax,
                            TotalNoTaxAmountPrice = output.AmountOfNoTax,
                            Value = output.Amount,
                            TaxRate = output.TaxRate * 100,
                            Code = output.CustomizeInvoiceCode,
                            Status = item.Status,
                            TaxTateCodeId = output.TaxTateCodeId,
                            RowNo = output.RowNo,
                            Remark = ic.Remark,
                            ProjectName = ic.ProjectName
                        };



            #region 查询条件 
            if (input.IsStrategy == null || input.IsStrategy.Value)
            {
                var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    Query = Query.Where(z => 1 != 1);
                }
                if (user.Data.List.First().InstitutionType == 4)
                {
                    if (user.Data.List.First().Institutions.Any())
                    {
                        var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id).ToList();
                        if (serviceIds != null)
                        {
                            var invoiceNos = from c in _db.Credits
                                             join ic in _db.InvoiceCredits on c.Id equals ic.CreditId into icGroup
                                             from ic in icGroup.DefaultIfEmpty()
                                             where serviceIds.ToHashSet().Contains(c.ServiceId)
                                             select ic.InvoiceNo;

                            Query = Query.Where(t => invoiceNos.Contains(t.InvoiceNo));
                        }
                    }
                }
                else
                {
                    #region 获取用户数据策略 
                    var strategryquery = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                    Query = await AddStrategyQueryAsync(strategryquery, Query);
                    #endregion
                }
            }

            if (input.Status == 1)
            {
                Query = Query.Where(x => x.Status == CustomizeInvoiceStatusEnum.Cancel);
            }
            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.InvoiceNo) && x.InvoiceNo == input.InvoiceNo);
            }
            if (!string.IsNullOrEmpty(input.InvoiceCode))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.InvoiceCode) && EF.Functions.Like(x.InvoiceCode, $"{input.InvoiceCode}%"));
            }
            if (!string.IsNullOrEmpty(input.Type))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.Type) && x.Type.Equals(input.Type));
            }
            if (!string.IsNullOrEmpty(input.BillCode))
            {
                var billCodes = await _db.CustomizeInvoiceDetail.Where(p => p.CreditBillCode.Contains(input.BillCode)).Select(p => p.CreditBillCode).ToListAsync();
                if (billCodes != null && billCodes.Count > 0)
                {
                    Query = Query.Where(x => !string.IsNullOrEmpty(x.BillCode) && billCodes.Contains(x.BillCode));
                }
                else
                {
                    Query = Query.Where(x => 1 != 1);
                }
            }
            if (!string.IsNullOrEmpty(input.RelateCode))
            {
                var billCodes = await _db.CustomizeInvoiceDetail.Where(p => p.RelateCode.Contains(input.RelateCode)).Select(p => p.CreditBillCode).ToListAsync();
                if (billCodes != null && billCodes.Count > 0)
                {
                    Query = Query.Where(x => !string.IsNullOrEmpty(x.BillCode) && billCodes.Contains(x.BillCode));
                }
                else
                {
                    Query = Query.Where(x => 1 != 1);
                }
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                var billCodes = await _db.CustomizeInvoiceDetail.Where(p => p.OrderNo.Contains(input.OrderNo)).Select(p => p.CreditBillCode).ToListAsync();
                if (billCodes != null && billCodes.Count > 0)
                {
                    Query = Query.Where(x => !string.IsNullOrEmpty(x.BillCode) && billCodes.Contains(x.BillCode));
                }
                else
                {
                    Query = Query.Where(x => 1 != 1);
                }
            }
            if (input.CustomerId.HasValue)
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.CustomerId) && x.CustomerId == input.CustomerId.ToString());
            }
            if (!string.IsNullOrEmpty(input.HospitalName))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.HospitalName.Contains(input.HospitalName));
            }
            if (input.ServiceId.HasValue)
            {
                var invoiceNos = from c in _db.Credits
                                 join ic in _db.InvoiceCredits on c.Id equals ic.CreditId into icGroup
                                 from ic in icGroup.DefaultIfEmpty()
                                 where c.ServiceId == input.ServiceId
                                 select ic.InvoiceNo;

                Query = Query.Where(t => invoiceNos.Contains(t.InvoiceNo));
                //Query = Query.Where(x => x.ServiceId.HasValue && x.ServiceId == input.ServiceId);
            }
            if (!string.IsNullOrEmpty(input.ProductName))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.ProductName) && x.ProductName.Contains(input.ProductName));
            }
            if (!string.IsNullOrEmpty(input.ProductNo))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.ProductNo) && x.ProductNo.Contains(input.ProductNo));
            }
            if (!string.IsNullOrEmpty(input.Specification))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.Specification) && x.Specification.Contains(input.Specification));
            }
            if (!string.IsNullOrWhiteSpace(input.billDateBeging) && !string.IsNullOrWhiteSpace(input.billDateEnd))
            {
                var dateTime = new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(long.Parse(input.billDateBeging)).AddHours(8);
                var endTime = dateTime.AddMilliseconds(long.Parse(input.billDateEnd)).AddHours(8);
                Query = Query.Where(z => z.InvoiceTime <= endTime && z.InvoiceTime >= startTime);
                //Query = Query.Where(z => z.InvoiceTime <= DateTimeHelper.GetDateTime(Convert.ToInt64(input.billDateEnd)) && z.InvoiceTime >= DateTimeHelper.GetDateTime(Convert.ToInt64(input.billDateBeging)));
            }
            if (!string.IsNullOrWhiteSpace(input.CreateDateBeging) && !string.IsNullOrWhiteSpace(input.CreateDateEnd))
            {
                Query = Query.Where(z => z.CreatedTime <= DateTimeHelper.GetDateTime(Convert.ToInt64(input.CreateDateEnd)) && z.CreatedTime >= DateTimeHelper.GetDateTime(Convert.ToInt64(input.CreateDateBeging)));
            }
            if (input.CompanyId.HasValue)
            {
                Query = Query.Where(x => x.CompanyId.HasValue && x.CompanyId == input.CompanyId);
            }
            if (!string.IsNullOrEmpty(input.BusinessDeptId))
            {
                Query = Query.Where(x => x.BusinessDeptId == input.BusinessDeptId);
            }
            if (!string.IsNullOrEmpty(input.Code))
            {
                Query = Query.Where(x => !string.IsNullOrEmpty(x.Code) && EF.Functions.Like(x.Code, $"%{input.Code}%"));
            }
            if (!string.IsNullOrEmpty(input.searchKey))
            {
                Query = Query.Where(x =>
                (!string.IsNullOrEmpty(x.Code) && x.Code.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.CompanyName) && x.CompanyName.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.CustomerName) && x.CustomerName.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.InvoiceNo) && x.InvoiceNo.Equals(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.ProductName) && x.ProductName.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.Specification) && x.Specification.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.ProductNo) && x.ProductNo.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.BusinessDeptFullName) && x.BusinessDeptFullName.Contains(input.searchKey)) ||
                (!string.IsNullOrEmpty(x.ServiceName) && x.ServiceName.Contains(input.searchKey)));
            }
            if (!string.IsNullOrEmpty(input.ProjectName))
            {
                Query = Query.Where(x => EF.Functions.Like(x.ProjectName, $"%{input.ProjectName}%"));
            }
            #endregion

            #region 排序 
            Query = Query.OrderByDescending(z => z.CreatedTime);
            #endregion

            if (isExport)
            {
                var exportList = await Query.ToListAsync();
                await InitServerName(exportList);
                return (exportList, exportList.Count());
            }

            int count = await Query.CountAsync();
            var list = await Query.Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();
            await InitServerName(list);
            return (list, count);
        }

        private async Task InitServerName(List<InvoiceCreditQueryListOutput> list)
        {
            var invoiceNos = list.Select(p => p.InvoiceNo).ToList();
            var temps = await _db.InvoiceCredits.
                            Include(p => p.Credit).
                            Where(p => invoiceNos.Contains(p.InvoiceNo)).
                            Select(p => new { p.Credit.ServiceName, p.InvoiceNo }).ToListAsync();
            var billCodes = list.Where(p => !string.IsNullOrEmpty(p.BillCode) && p.BillCode.Contains(','))?.Select(o => o.BillCode.Split(',')[0]).ToList();
            var allBillCodes = list.Where(p => !string.IsNullOrEmpty(p.BillCode))?.Select(o => o.BillCode).ToList();
            List<CreditPo> credits = new List<CreditPo>();
            List<CustomizeInvoiceDetailPo> details = new List<CustomizeInvoiceDetailPo>();
            if (allBillCodes != null && allBillCodes.Count > 0)
            {
                details = await _db.CustomizeInvoiceDetail.Where(p => allBillCodes.Contains(p.CreditBillCode)).ToListAsync();
            }
            foreach (var item in list)
            {
                var serviceNameLst = temps.Where(p => p.InvoiceNo == item.InvoiceNo).Select(p => p.ServiceName).Distinct().ToList();
                item.ServiceName = string.Join(",", serviceNameLst);

                var detail = details.FirstOrDefault(p => p.CreditBillCode == item.BillCode);
                if (detail != null)
                {
                    item.OrderNo = detail.OrderNo;
                    item.RelateCode = detail.RelateCode;
                }
            }
        }

        /// <summary>
        /// 根据订单号得到应收
        /// </summary>
        /// <param name="orderNos"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<CreditOfProjectOutput>>> GetCreditByOrderNos(List<string> orderNos)
        {
            var ret = BaseResponseData<List<CreditOfProjectOutput>>.Success("操作成功！");
            if (orderNos != null && orderNos.Any())
            {
                var credits = await _db.Credits.Where(x => orderNos.Contains(x.OrderNo)).ToListAsync();
                var creditNos = credits.Select(p => p.BillCode).ToList();
                var abatements = await _db.Abatements.Where(p => creditNos.Contains(p.CreditBillCode) || creditNos.Contains(p.DebtBillCode)).ToListAsync();
                var data = new List<CreditOfProjectOutput>();
                foreach (var item in credits)
                {
                    var pos = abatements.Where(x => x.CreditBillCode == item.BillCode || x.DebtBillCode == item.BillCode).ToList();
                    data.Add(new CreditOfProjectOutput
                    {
                        CreditType = item.CreditType,
                        CreditCode = item.BillCode,
                        CustomerId = item.CustomerId,
                        CustomerName = item.CustomerName,
                        CompanyName = item.CompanyName,
                        CompanyId = item.CompanyId,
                        TotalValue = item.Value,
                        ActualTotalValue = pos != null && pos.Any() ? pos.Sum(p => p.Value) : 0,
                        AbatementTime = pos != null && pos.Any() ? pos.Max(p => p.CreatedTime) : DateTimeOffset.UtcNow
                    });
                }
                ret.Data = data;
            }
            else
            {
                ret = BaseResponseData<List<CreditOfProjectOutput>>.Failed(500, "操作失败，原因：参数不能为空");
            }
            return ret;
        }

        /// <summary>
        /// 根据订单号得到应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<CreditOfProjectOutput>>> GetCreditByInvoiceNo(CreditOfDeliveryInput input)
        {
            var ret = BaseResponseData<List<CreditOfProjectOutput>>.Success("操作成功！");
            var credits = await (from p in _db.InvoiceCredits
                                 join c in _db.CustomizeInvoiceItem
                                 on p.CustomizeInvoiceCode equals c.Code
                                 where input.InvoiceNos.Contains(p.InvoiceNo) && c.CompanyId == input.CompanyId
                                 select p.Credit).ToListAsync();
            var creditNos = credits.Select(p => p.BillCode).ToList();
            var abatements = await _db.Abatements.Where(p => creditNos.Contains(p.CreditBillCode) || creditNos.Contains(p.DebtBillCode)).ToListAsync();
            var data = new List<CreditOfProjectOutput>();
            foreach (var item in credits)
            {
                var pos = abatements.Where(x => x.CreditBillCode == item.BillCode || x.DebtBillCode == item.BillCode).ToList();
                data.Add(new CreditOfProjectOutput
                {
                    CreditType = item.CreditType,
                    CreditCode = item.BillCode,
                    CustomerId = item.CustomerId,
                    CustomerName = item.CustomerName,
                    CompanyName = item.CompanyName,
                    CompanyId = item.CompanyId,
                    TotalValue = item.Value,
                    ActualTotalValue = pos != null && pos.Any() ? pos.Sum(p => p.Value) : 0,
                    AbatementTime = pos != null && pos.Any() ? pos.Max(p => p.CreatedTime) : DateTimeOffset.UtcNow
                });
            }
            ret.Data = data;
            return ret;
        }
        /// <summary>
        /// 根据发票号获取终端客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<CreditOfHospitalOutput>> GetCreditHospitalByInvoiceNo(CreditOfHospitalInput input)
        {

            var credits = await (from ic in _db.InvoiceCredits
                                 join c in _db.Credits
                                 on ic.CreditId equals c.Id
                                 where input.InvoiceNos.ToHashSet().Contains(ic.InvoiceNo)
                                 select new { ic.InvoiceNo, c.HospitalId, c.HospitalName }).Distinct().ToListAsync();
            var data = new List<CreditOfHospitalOutput>();
            foreach (var item in credits)
            {
                data.Add(new CreditOfHospitalOutput
                {
                    InvoiceNo = item.InvoiceNo,
                    HospitalId = item.HospitalId,
                    HospitalName = item.HospitalName
                });
            }
            return data;
        }

        /// <summary>
        /// 应收单列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditQueryListOutput>, int)> GetListAsync(CreditQueryInput query)
        {
            Expression<Func<CreditPo, bool>> exp = z => 1 == 1;
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            #region 查询条件
            exp = await InitExp(query, exp, strategry);
            #endregion

            IQueryable<CreditPo> baseQuery = _db.Credits.Where(exp).AsNoTracking();
            //运营制作未开票
            if (!string.IsNullOrWhiteSpace(query.isNotInvoice) && query.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed)
            {
                baseQuery = baseQuery.Where(p => p.InvoiceStatus != InvoiceStatusEnum.invoiced);
                if (query.isContainZero != 1)
                {
                    baseQuery = baseQuery.Where(p => p.Value != 0);
                }

            }
            #region 排序
            if (query.sort != null && query.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<CreditPo>(query.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<CreditQueryListOutput>()).ToListAsync();

            await InitCreditInfo(list);
            return (list, count);

        }
        /// <summary>
        /// 预开票关联应收查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<(List<CreditQueryListOutput>, int)> GetListForPreInvoiceAsync(CreditForPreInvoiceInput input)
        {
            //根据发票号查询预开票单
            var preCustomizeInvoiceItemPo = await _db.PreCustomizeInvoiceItem.FirstOrDefaultAsync(p => p.Code == input.PreCustomizeInvoiceCode);
            if (preCustomizeInvoiceItemPo == null)
            {
                throw new Exception($"操作失败，原因：没有找到{input.PreCustomizeInvoiceCode}预开票单");
            }
            var credits = await _db.Credits.Where(p => p.CompanyId == preCustomizeInvoiceItemPo.CompanyId &&
                                                   p.CustomerId == preCustomizeInvoiceItemPo.CustomerId &&
                                                   p.ProjectId == preCustomizeInvoiceItemPo.ProjectId &&
                                                   p.InvoiceStatus != InvoiceStatusEnum.invoiced &&
                                                   p.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed)
                                       .Select(p => new CreditQueryListOutput
                                       {
                                           BillCode = p.BillCode,
                                           Value = p.Value,
                                           Id = p.Id
                                       }).ToListAsync();
            var creditIds = credits.Select(p => p.Id).ToHashSet();
            //查询已开票的应收明细
            var creditDetailsOfInvoice = await _db.CreditDetails.Where(p => creditIds.Contains(p.CreditId) && p.InvoiceAmount > 0).ToListAsync();
            var creditIdsOfInvoice = creditDetailsOfInvoice.Select(p => p.CreditId).ToHashSet();
            credits = credits.Where(p => !creditIdsOfInvoice.Contains(p.Id)).ToList();
            return (credits, credits.Count());

        }

        /// <summary>
        /// 应收单列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditQueryListOutput>, int)> GetListByRecognizeReceiveAsync(CreditQueryInput query)
        {
            Expression<Func<CreditPo, bool>> exp = z => 1 == 1;
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            #region 查询条件
            exp = await InitExp(query, exp, strategry);
            #endregion

            IQueryable<CreditPo> baseQuery = _db.Credits.Where(exp).AsNoTracking();
            //运营制作未开票
            if (!string.IsNullOrWhiteSpace(query.isNotInvoice) && query.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed)
            {
                baseQuery = baseQuery.Where(p => p.InvoiceStatus != InvoiceStatusEnum.invoiced);
                if (query.isContainZero != 1)
                {
                    baseQuery = baseQuery.Where(p => p.Value != 0);
                }
            }
            #region 排序
            if (query.sort != null && query.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<CreditPo>(query.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<CreditQueryListOutput>()).ToListAsync();
            return (list, count);
        }



        private async Task InitCreditInfo(List<CreditQueryListOutput> list)
        {
            //获取已冲销金额
            var creditCodes = list.Select(p => p.BillCode).ToList();
            var hasCreditIds = list.Select(p => p.Id).ToHashSet();
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                return;
            }
            if (creditCodes == null || !creditCodes.Any())
            {
                return;
            }
            if (hasCreditIds == null || !hasCreditIds.Any())
            {
                return;
            }
            var hasCreditCodes = creditCodes.ToHashSet();
            var debtDetails = new List<DebtDetailPo>();
            if (user.Data.List.First().InstitutionType == 4)
            {
                debtDetails = await _db.DebtDetails.Where(x => x.CreditId.HasValue && hasCreditIds.Contains(x.CreditId.Value)).AsNoTracking().ToListAsync();
            }
            var abatments = await _db.Abatements.Where(p => creditCodes.Contains(p.DebtBillCode) || creditCodes.Contains(p.CreditBillCode))
                                     .Select(p => new { p.DebtBillCode, p.CreditBillCode, p.Value }).ToListAsync();
            //获取已开发票金额
            var creditIds = list.Select(p => p.Id).ToList();
            var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CreditId != null && creditIds.Contains((Guid)p.CreditId) && p.IsCancel != true)
                                     .Select(p => new { p.CreditId, p.InvoiceAmount, p.CreditAmount, p.Type }).ToListAsync();

            var groupedResults = list.GroupBy(p => new { p.CompanyId, p.CustomerId, p.ServiceId })
                                                        .Select(g => new
                                                        {
                                                            ServiceId = g.Key.ServiceId.HasValue ? g.Key.ServiceId.Value.ToString().ToUpper() : string.Empty,
                                                            CompanyId = g.Key.CompanyId.HasValue ? g.Key.CompanyId.Value.ToString().ToUpper() : string.Empty,
                                                            CustomerId = g.Key.CustomerId.HasValue ? g.Key.CustomerId.Value.ToString().ToUpper() : string.Empty,
                                                            Items = g.ToList()
                                                        })
                                                        .ToList();

            var purchaseConfigBox = new List<PurchaseConfigBox>();
            foreach (var group in groupedResults)
            {
                var hiddenSaleAccountInfo = false;
                //查询是否采购子系统配置
                var puaInput = new SubSysRelaQueryInput()
                {
                    ServiceId = group.ServiceId,
                    CompanyId = group.CompanyId,
                    CustomerId = group.CustomerId,
                    UserId = _appServiceContextAccessor.Get().UserId
                };
                var puaRet = await _purchaseExcuteApiClient.GetSubSysRelaConfig(puaInput);
                if (puaRet.Code == CodeStatusEnum.Success && puaRet.Data != null && puaRet.Data.List != null && puaRet.Data.List.Any())
                {
                    hiddenSaleAccountInfo = puaRet.Data.List[0].AuditControls.HiddenSaleAccountInfo.HasValue && !puaRet.Data.List[0].AuditControls.HiddenSaleAccountInfo.Value ? false : true;
                }
                purchaseConfigBox.Add(new PurchaseConfigBox
                {
                    ServiceId = group.ServiceId,
                    CompanyId = group.CompanyId,
                    CustomerId = group.CustomerId,
                    UserId = _appServiceContextAccessor.Get().UserId,
                    HiddenSaleAccountInfo = hiddenSaleAccountInfo
                });
            }

            foreach (var credit in list)
            {
                credit.AbatmentAmount = abatments.Where(t => t.DebtBillCode == credit.BillCode || t.CreditBillCode == credit.BillCode).Sum(t => t.Value);
                credit.InvoiceAmount = invoiceCredits.Where(t => t.CreditId == credit.Id).Sum(p => p.CreditAmount.Value);
                if (invoiceCredits.FirstOrDefault(t => t.CreditId == credit.Id) != null)
                {
                    switch (invoiceCredits.FirstOrDefault(t => t.CreditId == credit.Id).Type)
                    {
                        case "电子普通发票":
                            credit.InvoiceType = InvoiceTypeEnum.DZUniversal;
                            break;
                        case "电子专用发票":
                            credit.InvoiceType = InvoiceTypeEnum.DZSpecial;
                            break;
                        case "纸质普通发票":
                            credit.InvoiceType = InvoiceTypeEnum.DZUniversal;
                            break;
                        case "纸质专用发票":
                            credit.InvoiceType = InvoiceTypeEnum.ZZSpecial;
                            break;
                        case "增值税普通发票(卷票)":
                            credit.InvoiceType = InvoiceTypeEnum.RollTicket;
                            break;
                        case "数电票(增值税专用发票)":
                            credit.InvoiceType = InvoiceTypeEnum.DigitalCircuitTiket;
                            break;
                        case "数电票(普通发票)":
                            credit.InvoiceType = InvoiceTypeEnum.DigitalCircuitUniversal;
                            break;
                        default:
                            break;
                    }
                }
                if (credit.ServiceId.HasValue && credit.CompanyId.HasValue && credit.CustomerId.HasValue)
                {
                    var singleConfig = purchaseConfigBox.FirstOrDefault(x => x.ServiceId == credit.ServiceId.Value.ToString().ToUpper() && x.CompanyId == credit.CompanyId.Value.ToString().ToUpper() && x.CustomerId == credit.CustomerId.Value.ToString().ToUpper());
                    // #117246 【小】客户端，销售账期对应的应收的已冲销和余额列不显示金额
                    if (debtDetails != null && debtDetails.Any() && singleConfig != null)
                    {
                        var currentDebtDetailsByAccountPeriodType = debtDetails.Where(x => x.AccountPeriodType == 2 && x.CreditId == credit.Id).ToList();
                        credit.IsContainsAccountPeriodTypeBySale = currentDebtDetailsByAccountPeriodType.Any() && singleConfig.HiddenSaleAccountInfo;
                    }
                }
            }
        }

        /// <summary>
        ///导出关联发票的产品清单
        /// </summary>
        /// <returns></returns>        
        public async Task<MemoryStream> CreditHasInvoiceExport(CreditQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();
                var (list, count) = await GetListAsync(query);
                //if (list == null || list.Count <= 0)
                //{
                //    return stream;
                //}
                var creditIds = list.Select(t => (Guid?)t.Id).ToList();
                var invoices = await _db.InvoiceCredits.Where(z => z.CreditId != null && creditIds.Contains(z.CreditId)).AsNoTracking().ToListAsync();
                var ciCodes = invoices.Select(x => x.CustomizeInvoiceCode).ToList();
                var ciItems = await _db.CustomizeInvoiceItem.Where(x => ciCodes.Contains(x.Code)).AsNoTracking().ToListAsync();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "关联单号");
                    worksheet.SetValue(1, columnIndex++, "客户");
                    worksheet.SetValue(1, columnIndex++, "单据日期");
                    worksheet.SetValue(1, columnIndex++, "销售子系统");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "订单号");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    //worksheet.SetValue(1, columnIndex++, "终端医院");
                    worksheet.SetValue(1, columnIndex++, "应收类型");
                    worksheet.SetValue(1, columnIndex++, "核算部门");
                    worksheet.SetValue(1, columnIndex++, "项目名称");
                    worksheet.SetValue(1, columnIndex++, "金额");
                    worksheet.SetValue(1, columnIndex++, "已冲销");
                    worksheet.SetValue(1, columnIndex++, "余额");
                    worksheet.SetValue(1, columnIndex++, "确认收入");
                    worksheet.SetValue(1, columnIndex++, "终端医院");
                    worksheet.SetValue(1, columnIndex++, "订货人");
                    worksheet.SetValue(1, columnIndex++, "客户订单号");
                    worksheet.SetValue(1, columnIndex++, "发票号");
                    worksheet.SetValue(1, columnIndex++, "发票代码");
                    worksheet.SetValue(1, columnIndex++, "开票时间");
                    worksheet.SetValue(1, columnIndex++, "开票金额");
                    worksheet.SetValue(1, columnIndex++, "应收单金额");
                    worksheet.SetValue(1, columnIndex++, "红冲状态");
                    worksheet.SetValue(1, columnIndex++, "蓝字发票号");
                    worksheet.SetValue(1, columnIndex++, "状态");

                    var index = 2;
                    list.ForEach(p =>
                    {
                        columnIndex = 1;
                        var invoiceInfos = invoices.Where(z => z.CreditId != null && p.Id == z.CreditId).ToList();

                        worksheet.SetValue(index, columnIndex++, p.BillCode);
                        worksheet.SetValue(index, columnIndex++, p.RelateCode);
                        worksheet.SetValue(index, columnIndex++, p.CustomerName);
                        worksheet.SetValue(index, columnIndex++, p.BillDate.Value.ToString("yyyy-MM-dd"));
                        worksheet.SetValue(index, columnIndex++, p.SaleSystemName);
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.OrderNo);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        //worksheet.SetValue(index, columnIndex++, p.终端医院);//当前release没有这个数据
                        worksheet.SetValue(index, columnIndex++, p.CreditTypeStr);
                        worksheet.SetValue(index, columnIndex++, p.BusinessDeptFullName);
                        worksheet.SetValue(index, columnIndex++, p.ProjectName);
                        worksheet.SetValue(index, columnIndex++, p.Value); //Value
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.AbatmentAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.Value > 0 ? p.LeftAmount : -p.LeftAmount); //Value
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.IsSureIncome >= 1 ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.HospitalName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerPersonName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerOrderCode);
                        if (invoiceInfos == null || invoiceInfos.Count <= 0)
                        {
                            index++;
                        }
                        else
                        {
                            foreach (var item in invoiceInfos)
                            {
                                var columnIndexSec = columnIndex;
                                var single = ciItems.FirstOrDefault(x => x.Code == item.CustomizeInvoiceCode);
                                var changeStr = single != null ? single.ChangedStatus.GetDescription() : string.Empty;
                                if (string.IsNullOrEmpty(changeStr))
                                {
                                    changeStr = "未红冲";
                                }
                                worksheet.SetValue(index, columnIndex++, item.InvoiceNo);
                                worksheet.SetValue(index, columnIndex++, item.InvoiceCode);
                                worksheet.SetValue(index, columnIndex++, item.InvoiceTime.Value.ToString("yyyy-MM-dd"));
                                worksheet.SetValue(index, columnIndex++, item.InvoiceAmount);
                                worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, columnIndex++, item.CreditAmount);
                                worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, columnIndex++, changeStr);
                                worksheet.SetValue(index, columnIndex++, single != null ? single.InvoiceNo : string.Empty);
                                worksheet.SetValue(index, columnIndex++, item.IsCancel == true ? "已作废" : "已开票");
                                columnIndex = columnIndexSec;
                                index++;
                            }
                        }
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        private async Task<Expression<Func<CreditPo, bool>>> InitExp(CreditQueryInput query, Expression<Func<CreditPo, bool>> exp, StrategyQueryOutput? strategry)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
                else
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                        }
                        if (key.ToLower() == "service")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.ServiceId.Value));
                            }
                        }
                        else if (key.ToLower() == "project")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => !t.ProjectId.HasValue || strategList.Contains(t.ProjectId.Value));
                            }
                        }
                        if (key.ToLower() == "accountingdept")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                exp = exp.And(t => strategList.Contains(t.BusinessDeptId));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CustomerId.Value));
                            }
                        }
                    }
                }
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        exp = exp.And(t => serviceIds.Contains(t.ServiceId));
                    }
                }
                if (!string.IsNullOrEmpty(query.CreatedByName))
                {
                    var userRet = await _bDSApiClient.GetSmallUsersByDisplayNames(new List<string> { query.CreatedByName.Trim() });
                    var userNames = userRet.Select(t => t.Name).ToList();
                    if (userNames != null && userNames.Any())
                    {
                        exp = exp.And(z => userNames.ToHashSet().Contains(z.CreatedBy));
                    }
                    else
                    {
                        exp = exp.And(z => false);
                        return exp;
                    }
                }
            }
            if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => EF.Functions.Like(z.BillCode, $"%{query.searchKey}%")
                       || EF.Functions.Like(z.ServiceName, $"%{query.searchKey}%")
                       || EF.Functions.Like(z.CompanyName, $"%{query.searchKey}%")
                       || EF.Functions.Like(z.CustomerName, $"%{query.searchKey}%"));
            }
            if (query.BillDateStart != null && query.BillDateEnd != null)
            {
                exp = exp.And(z => (z.BillDate != null && z.BillDate.Value >= query.BillDateStart && z.BillDate.Value <= query.BillDateEnd));
            }
            if (query.IsNoNeedInvoice.HasValue && query.IsNoNeedInvoice == IsNoNeedInvoiceEnum.Need)
            {
                exp = exp.And(z => !z.IsNoNeedInvoice.HasValue || z.IsNoNeedInvoice.Value == IsNoNeedInvoiceEnum.Need);
            }
            if (query.IsNoNeedInvoice.HasValue && query.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed)
            {
                exp = exp.And(z => z.IsNoNeedInvoice == IsNoNeedInvoiceEnum.NoNeed);
            }
            if (!string.IsNullOrWhiteSpace(query.RelateCode))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.RelateCode) && z.RelateCode.Equals(query.RelateCode));
            }
            if (!string.IsNullOrWhiteSpace(query.OriginOrderNo))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.OriginOrderNo) && z.OriginOrderNo.Equals(query.OriginOrderNo));
            }
            if (!string.IsNullOrWhiteSpace(query.ShipmentCode))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.ShipmentCode) && z.ShipmentCode.Equals(query.ShipmentCode));
            }
            if (!string.IsNullOrWhiteSpace(query.RedReversalConsumNo))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.RedReversalConsumNo) && z.RedReversalConsumNo.Equals(query.RedReversalConsumNo));
            }
            if (query.AbatedStatus.HasValue && (int)query.AbatedStatus > -1)
            {
                exp = exp.And(z => z.AbatedStatus == query.AbatedStatus);
            }
            if (query.CreatedBy != null && query.CreatedBy.Any())
            {
                exp = exp.And(z => query.CreatedBy.ToHashSet().Contains(z.CreatedBy));
            }
            if (query.InvoiceStatus.HasValue)
            {
                exp = exp.And(z => z.InvoiceStatus == query.InvoiceStatus);
            }
            if (query.IsSureIncome.HasValue)
            {
                exp = exp.And(z => z.IsSureIncome.HasValue && z.IsSureIncome == query.IsSureIncome);
            }
            if (query.ServicesId != null)
            {
                exp = exp.And(z => z.ServiceId == query.ServicesId);
            }
            if (!string.IsNullOrEmpty(query.ServicesName))
            {
                exp = exp.And(z => z.ServiceName != null && z.ServiceName.Contains(query.ServicesName));
            }
            if (query.CompanyId != null)
            {
                exp = exp.And(z => z.CompanyId == query.CompanyId);
            }
            if (query.CustomerId != null)
            {
                exp = exp.And(z => z.CustomerId == query.CustomerId);
            }
            if (!string.IsNullOrWhiteSpace(query.CreditType))
            {
                exp = exp.And(z => (int)z.CreditType == int.Parse(query.CreditType));
            }
            // 应收类型多选
            if (query.CreditTypes != null && query.CreditTypes.Any())
            {
                exp = exp.And(z => query.CreditTypes.Contains(((int)z.CreditType).ToString()));
            }
            if (!string.IsNullOrWhiteSpace(query.SaleSystemName))
            {
                exp = exp.And(z => z.SaleSystemName.Contains(query.SaleSystemName));
            }
            if (query.IsgreaterThanZero.HasValue && query.IsgreaterThanZero.Value)
            {
                exp = exp.And(z => z.Value > 0);
            }
            if (query.IsNoNeedInvoice != IsNoNeedInvoiceEnum.Need && ((query.IsChangeRelationship.HasValue && !query.IsChangeRelationship.Value) || !query.IsChangeRelationship.HasValue))
            {
                // 是否完全未开票
                if (query.IsNotHaveInvoice.HasValue && query.IsNotHaveInvoice.Value)
                {
                    var ids2 = await (from ic in _db.InvoiceCredits
                                      join c in _db.Credits on ic.CreditId equals c.Id
                                      where c.CompanyId == query.CompanyId &&
                                       c.CustomerId == query.CustomerId &&
                                       c.ProjectName == query.ProjectName
                                      select ic.CreditId).Distinct().ToListAsync();
                    exp = exp.And(z => !ids2.Contains(z.Id));
                }
                else
                {
                    //发票号
                    if (!string.IsNullOrWhiteSpace(query.InvoiceNo))
                    {
                        var (invoiceList, invoiceCount) = await _invoiceQueryService.GetListByInvoiceNoAsync(new InvoiceQueryInput() { InvoiceNo = query.InvoiceNo });
                        if (invoiceList != null && invoiceList.Any())
                        {
                            var CreditIds = invoiceList.Select(i => i.CreditId).ToList();
                            if (CreditIds != null && CreditIds.Any())
                            {
                                exp = exp.And(z => CreditIds.Contains(z.Id));
                            }
                            else { exp = exp.And(z => 1 != 1); }
                        }
                        else { exp = exp.And(z => 1 != 1); }
                    }
                }
            }
            //核算部门
            if (query.department != null && query.department != "")
            {
                //var projectList = await _projectMgntApiClient.GetProjectListByBusinessDeptIds(new List<string> { query.department });
                //if (projectList != null && projectList.Any())
                //{
                //    var projectIds = projectList.Select(p => p.Id).ToList();
                //    exp = exp.And(t => projectIds.Contains(t.ProjectId));
                //}
                exp = exp.And(z => EF.Functions.Like(z.BusinessDeptFullPath, $"%{query.department}%"));
            }
            if (!string.IsNullOrWhiteSpace(query.OrderNo))
            {
                exp = exp.And(z => EF.Functions.Like(z.OrderNo, $"%{query.OrderNo}%"));
            }
            if (!string.IsNullOrEmpty(query.Note))
            {
                exp = exp.And(z => EF.Functions.Like(z.Note, $"%{query.Note}%"));
            }
            if (!string.IsNullOrEmpty(query.DeptName))
            {
                exp = exp.And(z => EF.Functions.Like(z.DeptName, $"%{query.DeptName}%"));
            }
            if (!string.IsNullOrEmpty(query.ProjectName))
            {
                exp = exp.And(z => EF.Functions.Like(z.ProjectName, $"%{query.ProjectName}%"));
            }
            if (query.ProjectId.HasValue)
            {
                exp = exp.And(z => z.ProjectId == query.ProjectId.Value);
            }
            // 是否符合更换应收关系
            if (query.IsChangeRelationship.HasValue && query.IsChangeRelationship.Value)
            {
                if (!string.IsNullOrWhiteSpace(query.BillCode))
                {
                    exp = exp.And(z => z.BillCode != query.BillCode);
                }
                //不开票、冲销、认款
                var icIds = (await GetUninvoicedReturnIds(query.CompanyId, query.CustomerId, query.InvoiceNo)).ToHashSet();
                exp = exp.And(z => icIds.Any(p => p == z.Id));
                if (!string.IsNullOrWhiteSpace(query.NewBillCode))
                {
                    exp = exp.And(z => z.BillCode.Equals(query.NewBillCode));
                }
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(query.BillCode))
                {
                    exp = exp.And(z => z.BillCode.Equals(query.BillCode));
                }
            }
            if (query.IsSureIncomeDateStart != null && query.IsSureIncomeDateEnd != null)
            {
                exp = exp.And(z => z.IsSureIncomeDate != null && z.IsSureIncomeDate.Value >= query.IsSureIncomeDateStart && z.IsSureIncomeDate.Value <= query.IsSureIncomeDateEnd);
            }
            if (!string.IsNullOrEmpty(query.CustomerOrderCode))
            {
                exp = exp.And(z => EF.Functions.Like(z.CustomerOrderCode, $"%{query.CustomerOrderCode}%"));
            }
            if (!string.IsNullOrEmpty(query.CustomerPersonName))
            {
                exp = exp.And(z => EF.Functions.Like(z.CustomerPersonName, $"%{query.CustomerPersonName}%"));
            }
            if (query.CustomerIds != null && query.CustomerIds.Any())
            {
                exp = exp.And(z => z.CustomerId.HasValue && query.CustomerIds.ToHashSet().Contains(z.CustomerId.Value));
            }
            if (query.IsOverdue.HasValue && query.IsOverdue == 1)
            {
                exp = exp.And(z => z.ProbablyBackTime < DateTime.Today && z.AbatedStatus == AbatedStatusEnum.NonAbate);
            }
            else if (query.IsOverdue.HasValue && query.IsOverdue == 0)
            {
                exp = exp.And(z => z.ProbablyBackTime >= DateTime.Today);
            }
            else if (query.IsLossRecognition.HasValue && query.IsLossRecognition == false)
            {
                exp = exp.And(z => z.LossRecognitionValue == null || z.LossRecognitionValue == 0);
            }
            else if (query.IsLossRecognition.HasValue && query.IsLossRecognition == true)
            {
                exp = exp.And(z => z.LossRecognitionValue != 0 && z.LossRecognitionValue != null);
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                var list = await _db.Credits.Where(exp).AsNoTracking().ToListAsync();
                // 业务单元端不展示客户与终端医院不一致的数据
                var dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("NoDisplayPriceForTG");
                var dictionaryNames = dictionaryOutputs.Select(x => x.DictionaryName.ToUpper()).ToList();
                // 1.终端医院为空直接展示，终端医院等于客户显示
                var ids = new List<Guid>();
                var partIds1 = list.Where(x => string.IsNullOrEmpty(x.HospitalName) || x.CustomerName == x.HospitalName).Select(x => x.Id).ToList();
                ids.AddRange(partIds1);
                // 2.托管公司配置里存在的不显示
                var partIds2 = list.Where(x => !string.IsNullOrEmpty(x.HospitalName) && x.CustomerName != x.HospitalName && x.ServiceId.HasValue && x.CompanyId.HasValue && x.CustomerId.HasValue && !dictionaryNames.Contains(x.ServiceId.Value.ToString().ToUpper() + "_" + x.CompanyId.Value.ToString().ToUpper() + "_" + x.CustomerId.Value.ToString().ToUpper())).Select(x => x.Id).ToList();
                ids.AddRange(partIds2);
                if (ids.Any())
                {
                    exp = exp.And(x => ids.Contains(x.Id));
                }
                else
                {
                    exp = exp.And(x => 1 != 1);
                }
            }
            if (query.CreditSaleSubType.HasValue)
            {
                exp = exp.And(z => z.CreditSaleSubType == query.CreditSaleSubType);
            }
            if (query.PriceSource.HasValue)
            {
                exp = exp.And(z => z.PriceSource == query.PriceSource);
            }
            if (query.IsPreAssociation.HasValue && query.IsPreAssociation.Value)
            {
                exp = exp.And(z => z.InvoiceStatus != InvoiceStatusEnum.noninvoice ||
                  (z.InvoiceStatus != InvoiceStatusEnum.invoiced &&
                   z.CreditDetails != null &&
                   z.CreditDetails.Any() &&
                   z.CreditDetails.All(cd => cd.Amount == cd.NoInvoiceAmount)));
            }
            return exp;
        }


        /// <summary>
        /// 应收单列表查询Tab数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<CreditQueryListTabOutput>> GetTabCount(CreditQueryInput query)
        {
            var ret = BaseResponseData<CreditQueryListTabOutput>.Success("操作成功");
            Expression<Func<CreditPo, bool>> expAll = z => 1 == 1;
            Expression<Func<CreditPo, bool>> expAbated = z => 1 == 1;
            Expression<Func<CreditPo, bool>> expNonAbated = z => 1 == 1;
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            var data = new CreditQueryListTabOutput();
            #region 查询条件
            query.AbatedStatus = null;
            expAll = await InitExp(query, expAll, strategry);
            var list = await _db.Credits.Where(expAll).Select(p => p.AbatedStatus.Value).ToListAsync();
            data.AllCount = list.Count();
            data.AbatedCount = list.Where(x => x == AbatedStatusEnum.Abated).Count();
            data.NonAbatedCount = list.Where(x => x == AbatedStatusEnum.NonAbate).Count();
            #endregion
            ret.Data = data;
            return ret;
        }

        /// <summary>
        /// 应收明细列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditDetailQueryListOutput>, int)> GetListDetailAsync(CreditDetailQueryInput query)
        {
            IQueryable<DebtDetailPo> baseQuery = _db.DebtDetails.Where(z => z.CreditId == query.CreditId).AsNoTracking();
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = baseQuery.Count();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<CreditDetailQueryListOutput>()).ToListAsync();

            return (list, count);
        }
        /// <summary>
        /// 获取未冲销额度
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<decimal>> GetTotalValue(CreditQueryTotalInput input)
        {
            var ret = BaseResponseData<decimal>.Success("操作成功");
            if (input == null) throw new Exception("参数为空");
            var credits = await _db.Credits.Where(p => p.CustomerId == input.CustomerId && p.AbatedStatus == AbatedStatusEnum.NonAbate && p.CompanyId == input.CompanyId).ToListAsync();
            if (credits != null && credits.Any())
            {
                var creditCodes = credits.Select(p => p.BillCode);
                var abatementValue = await _db.Abatements.Where(p => creditCodes.Contains(p.CreditBillCode) && p.CreditType == "credit").SumAsync(p => p.Value);
                var creditValue = credits.Select(p => p.Value).Sum();
                ret.Data = creditValue - abatementValue;
                ret.Total = 1;
            }
            return ret;
        }
        public async Task<List<CreditQueryListOutput>> GetByRelateCode(string relateCode)
        {
            var credits = await _db.Credits.Where(p => p.RelateCode == relateCode).ToListAsync();
            return credits.Adapt<List<CreditQueryListOutput>>();
        }
        public async Task<List<CreditQueryListOutput>> GetByRelateCodes(List<string> relateCodes)
        {
            var credits = await _db.Credits.Where(p => relateCodes.Contains(p.RelateCode)).ToListAsync();
            return credits.Adapt<List<CreditQueryListOutput>>();
        }

        public async Task<List<Credit>> GetByOrderNo(string orderNo, Guid? serviceId)
        {
            var credits = await _db.Credits.Where(p => p.OrderNo == orderNo && p.ServiceId == serviceId && p.CreditType == CreditTypeEnum.sale).AsNoTracking().ToListAsync();
            return credits.Adapt<List<Credit>>();
        }
        /// <summary>
        /// 获取个人应收（旺店通）
        /// </summary>
        /// <param name="originOrderNos"></param>
        /// <returns></returns>
        public async Task<List<CreditWithDetailOutput>> GetByOriginOrderNos(List<string> originOrderNos)
        {
            var credits = await _db.Credits
                .Where(c => originOrderNos.Contains(c.OriginOrderNo)
                    && c.CreditSaleSubType == CreditSaleSubTypeEnum.personal)
                .Select(c => new CreditWithDetailOutput
                {
                    Id = c.Id,
                    BillCode = c.BillCode,
                    CompanyName = c.CompanyName,
                    OriginOrderNo = c.OriginOrderNo,
                    OrderNo = c.OrderNo,
                    Value = c.Value,
                    CreditType = (int)c.CreditType,
                    CreditSaleSubType = c.CreditSaleSubType,
                    RelateCode = c.RelateCode,
                    NameCode = c.NameCode,
                    IsSureIncome = c.IsSureIncome,
                    SaleSource = c.SaleSource,
                    CustomerOrderCode = c.CustomerOrderCode,
                    HospitalName = c.HospitalName
                })
                .ToListAsync();

            if (credits.Any())
            {
                var creditIds = credits.Select(c => c.Id).ToList();
                var details = await _db.CreditDetails
                    .Where(d => creditIds.Contains(d.CreditId))
                    .ToListAsync();
                foreach (var credit in credits)
                {
                    credit.Details = details
                        .Where(d => d.CreditId == credit.Id)
                        .ToList();
                }
            }

            return credits;
        }
        /// <summary>
        /// 获取客户负数应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<Credit>> GetNonAbated(GetCreditInput input)
        {
            if (!input.CustomerId.HasValue && string.IsNullOrEmpty(input.BillCode))
            {
                throw new Exception("操作失败：请选择客户后操作");
            }
            var credits = await _db.Credits.Where(p => p.AbatedStatus == AbatedStatusEnum.NonAbate &&
            p.CustomerId == input.CustomerId &&
            p.NameCode == input.NameCode &&
            p.BusinessDeptId == input.DeptId &&
            (string.IsNullOrEmpty(input.name) ? true : p.BillCode.Contains(input.name)) &&
            p.Value < 0).AsNoTracking().ToListAsync();
            var ret = credits.Adapt<List<Credit>>();
            if (credits != null && credits.Any())
            {
                var creditBillCodes = credits.Select(p => p.BillCode);
                var abatements = await _db.Abatements.Where(p => creditBillCodes.Contains(p.CreditBillCode) || creditBillCodes.Contains(p.DebtBillCode)).ToListAsync();
                foreach (var credit in ret)
                {
                    var abatment = abatements.Where(p => p.CreditBillCode == credit.BillCode || p.DebtBillCode == credit.BillCode).Sum(p => p.Value);
                    credit.NonAbatedValue = Math.Abs(credit.Value) - abatment;
                }
            }
            return ret;
        }

        /// <summary>
        /// 数据策略权限增加(销项发票)
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        private async Task<IQueryable<InvoiceCreditQueryListOutput>> AddStrategyQueryAsync(StrategyQueryInput queryModel, IQueryable<InvoiceCreditQueryListOutput> query)
        {
            if (queryModel != null)
            {
                var strategys = await _pCApiClient.GetStrategyAsync(queryModel);
                if (strategys != null)
                {
                    if (!strategys.RowStrategies.Keys.Contains("accountingDept") || !strategys.RowStrategies.Keys.Contains("company"))
                    {
                        query = query.Where(z => 1 != 1);
                        return query;
                    }
                    query = AnalysisStrategy(strategys.RowStrategies, query);
                }

            }

            return query;
        }
        /// <summary>
        /// 增加数据策略权限(销项发票)
        /// </summary>
        /// <param name="rowStrategies"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected IQueryable<InvoiceCreditQueryListOutput> AnalysisStrategy(Dictionary<string, List<string>> rowStrategies, IQueryable<InvoiceCreditQueryListOutput> query)
        {
            foreach (var key in rowStrategies.Keys)
            {
                if (key.ToLower() == "company")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].Select(s => Guid.Parse(s.ToLower())).ToHashSet();
                        query = query.Where(z => z.CompanyId != null && strategList.Contains(z.CompanyId.Value));
                    }
                }
                if (key.ToLower() == "accountingdept")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].Select(s => s.ToLower()).ToHashSet();
                        query = query.Where(z => z.BusinessDeptId != null && strategList.Contains(z.BusinessDeptId));
                    }
                }
                if (key.ToLower() == "customer")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].Select(s => s.ToLower()).ToHashSet();
                        query = query.Where(z => z.CustomerId != null && strategList.Contains(z.CustomerId.ToLower()));
                    }
                }
                if (key.ToLower() == "service")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].Select(s => Guid.Parse(s.ToLower())).ToHashSet();
                        query = query.Where(z => z.ServiceId != null && strategList.Contains(z.ServiceId.Value));
                    }
                }
            }
            return query;
        }

        public async Task<BaseResponseData<List<CreditAgeOutput>>> GetCreditAgeData(Guid companyId)
        {
            var creditQuery = _db.Credits.Where(p => p.CompanyId == companyId).AsQueryable();
            var creditCodes = creditQuery.Select(p => p.BillCode);
            var creditIds = creditQuery.Select(p => p.Id);
            var abatementQuery = _db.Abatements.Where(p => creditCodes.Contains(p.DebtBillCode) || creditCodes.Contains(p.CreditBillCode));
            var creditInvoiceQuery = _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value));
            var creditInvoices = await creditInvoiceQuery.ToListAsync();
            var credits = await creditQuery.ToListAsync();
            var abatements = await abatementQuery.ToListAsync();
            var data = new List<CreditAgeOutput>();
            credits.ForEach(p =>
            {
                var res = new CreditAgeOutput()
                {
                    CreditCode = p.BillCode,
                    CreditType = p.CreditType.GetDescription(),
                    Amount = p.Value,
                    CustomerName = p.CustomerName,
                    AbatementAmount = 0,
                    InvoiceTaxAmount = 0,
                    SureAndInvoiceAmount = 0,
                    SureAndNoInvoiceAmount = 0,
                    SureAndNoInvoiceTaxAmount = 0,
                    UnSureAmount = 0,
                    UnSureAndNoInvoiceTaxAmount = 0
                };
                var thisInvocieAmount = creditInvoices.Where(t => t.CreditId == p.Id).Sum(q => q.CreditAmount);
                if (p.IsSureIncome != 1)
                {
                    res.UnSureAmount = Math.Round(p.Value / 1.13m, 2);
                    res.UnSureAndNoInvoiceTaxAmount = Math.Round((p.Value - thisInvocieAmount ?? 0) * 0.13m, 2);
                }
                if (p.IsSureIncome == 1)
                {
                    res.SureAndNoInvoiceAmount = Math.Round((p.Value - thisInvocieAmount ?? 0) / 1.13m, 2);
                    res.SureAndInvoiceAmount = Math.Round(thisInvocieAmount ?? 0 / 1.13m, 2);
                    res.SureAndNoInvoiceTaxAmount = Math.Round((p.Value - thisInvocieAmount ?? 0) * 0.13m, 2);
                }
                res.InvoiceTaxAmount = thisInvocieAmount ?? 0 * 0.13m;
                var abateAmount = abatements.Where(t => t.DebtBillCode == p.BillCode || t.CreditBillCode == p.BillCode).Sum(q => q.Value);
                res.AbatementAmount = abateAmount;
                data.Add(res);
            });
            return new BaseResponseData<List<CreditAgeOutput>>() { Code = CodeStatusEnum.Success, Data = data, Message = "成功" };
        }


        /// <summary>
        /// 获取符合更换关系的应收单数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditQueryListOutput>, int)> GetCompliantRelationshipList(CreditQueryInput query)
        {
            Expression<Func<CreditPo, bool>> exp = z => 1 == 1;
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            #region 查询条件
            query.IsChangeRelationship = true;
            exp = await InitExp(query, exp, strategry);
            #endregion

            IQueryable<CreditPo> baseQuery = _db.Credits.Where(exp).AsNoTracking();
            //var sql = baseQuery.ToQueryString();


            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<CreditQueryListOutput>()).ToListAsync();

            return (list, count);
        }


        public async Task<List<Guid>> GetUninvoicedReturnIds(Guid? companyId, Guid? customerId, string? invoiceNo)
        {
            if (companyId == null || customerId == null)
            {
                return new List<Guid>();
            }

            // 定义强类型DTO
            var baseQuery = _db.Credits
                .Where(c => c.BillCode != null
                    && c.CompanyId == companyId
                    && c.CustomerId == customerId
                    && c.AbatedStatus != AbatedStatusEnum.Abated
                    && c.InvoiceStatus != InvoiceStatusEnum.invoiced
                    && c.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed
                    && (c.CreditType == CreditTypeEnum.sale
                        || c.CreditType == CreditTypeEnum.selforder
                        || c.CreditType == CreditTypeEnum.selfreturn))
                .Select(c => new CreditBatchDto { Id = c.Id, BillCode = c.BillCode });

            // 分批次处理
            var batchSize = 3000;
            var result = new List<Guid>();
            var totalCount = await baseQuery.CountAsync();

            for (int i = 0; i < totalCount; i += batchSize)
            {
                var batch = await baseQuery
                    .Skip(i)
                    .Take(batchSize)
                    .ToListAsync();

                var validBatch = await ProcessBatch(batch, invoiceNo);
                result.AddRange(validBatch);
            }

            return result.Distinct().ToList();
        }

        private async Task<List<Guid>> ProcessBatch(List<CreditBatchDto> batch, string? invoiceNo)
        {
            var billCodes = batch.Select(x => x.BillCode).ToList();
            var ids = batch.Select(x => x.Id).ToList();

            // 1. 条件1：没有明细或所有明细NoInvoiceAmount等于Amount
            var ids1 = await _db.Credits
                .Where(c => ids.Contains(c.Id))
                .Where(c => !_db.CreditDetails.Any(cd => cd.CreditId == c.Id) ||
                           _db.CreditDetails.Where(cd => cd.CreditId == c.Id)
                               .All(cd => cd.NoInvoiceAmount == cd.Amount))
                .Select(c => c.Id)
                .ToListAsync();

            if (!ids1.Any()) return new List<Guid>();

            // 2. 条件2：不存在认款的应收
            var recognizedCodes = await _db.RecognizeReceiveDetailCredits
                .Where(r => billCodes.Contains(r.CreditCode))
                .Select(r => r.CreditCode)
                .Distinct()
                .ToListAsync();

            var ids2 = recognizedCodes.Any()
                ? await _db.Credits
                    .Where(c => ids1.Contains(c.Id) && !recognizedCodes.Contains(c.BillCode))
                    .Select(c => c.Id)
                    .ToListAsync()
                : ids1;

            if (!ids2.Any()) return new List<Guid>();

            // 3. 条件3：排除冲销记录
            var abatementCodes = await _db.Abatements
                .Where(a => (a.CreditType == "credit" && billCodes.Contains(a.CreditBillCode)) ||
                            (a.DebtType == "credit" && billCodes.Contains(a.DebtBillCode)))
                .Select(a => a.CreditType == "credit" ? a.CreditBillCode : a.DebtBillCode)
                .Distinct()
                .ToListAsync();

            var ids3 = abatementCodes.Any()
                ? await _db.Credits
                    .Where(c => ids2.Contains(c.Id) && !abatementCodes.Contains(c.BillCode))
                    .Select(c => c.Id)
                    .ToListAsync()
                : ids2;

            if (!ids3.Any()) return new List<Guid>();

            // 4. 条件4：排除负数应收的认款单
            var negativeReceivableCodes = await _db.RecognizeReceiveItems
                .Where(r => r.Type == "负数应收" &&
                           r.Status != RecognizeReceiveItemStatusEnum.Canceled &&
                           billCodes.Contains(r.ReceiveCode))
                .Select(r => r.ReceiveCode)
                .Distinct()
                .ToListAsync();

            var ids4 = negativeReceivableCodes.Any()
                ? await _db.Credits
                    .Where(c => ids3.Contains(c.Id) && !negativeReceivableCodes.Contains(c.BillCode))
                    .Select(c => c.Id)
                    .ToListAsync()
                : ids3;

            if (!ids4.Any()) return new List<Guid>();

            // 5. 条件5：排除已关联发票的应收（修正Except问题）
            if (invoiceNo != null)
            {
                var invoicedIds = await _db.InvoiceCredits
                    .Where(x => x.InvoiceNo == invoiceNo && x.CreditId.HasValue && ids4.Contains(x.CreditId.Value))
                    .Select(x => x.CreditId)
                    .ToListAsync();

                if (invoicedIds.Any())
                {
                    // 使用HashSet处理Except
                    var invoicedIdsSet = invoicedIds.ToHashSet();
                    ids4 = ids4.Where(id => !invoicedIdsSet.Contains(id)).ToList();
                }
            }

            return ids4;
        }


        #region 查询符合条件的应收单id（返回id集合）
        ///// <summary>
        ///// 查询符合条件的应收单id（返回id集合）
        ///// </summary>
        ///// <param name="companyId">公司id</param>
        ///// <param name="customerId">客户id</param>
        ///// <param name="invoiceNo">发票号</param>
        ///// 更换应收关系必须存在公司客户id
        ///// <returns></returns>

        //public async Task<List<Guid>> GetUninvoicedReturnIds(Guid? companyId, Guid? customerId, string? invoiceNo)
        //{
        //    if (companyId == null || customerId == null)
        //    {
        //        return new List<Guid>();
        //    }

        //    // 优化1: 使用游标分页代替Skip/Take
        //    var baseQuery = _db.Credits
        //        .Where(c => c.BillCode != null
        //            && c.CompanyId == companyId
        //            && c.CustomerId == customerId
        //            && c.AbatedStatus != AbatedStatusEnum.Abated
        //            && c.InvoiceStatus != InvoiceStatusEnum.invoiced
        //            && c.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed
        //            && (c.CreditType == CreditTypeEnum.sale
        //                || c.CreditType == CreditTypeEnum.selforder
        //                || c.CreditType == CreditTypeEnum.selfreturn))
        //        .OrderBy(c => c.Id)  // 必须排序才能使用游标分页
        //        .Select(c => new CreditBatchDto { Id = c.Id, BillCode = c.BillCode });

        //    // 优化2: 提前获取排除集合（整个客户范围）
        //    var (excludeBillCodes, excludeCreditIds) = await GetExclusionSets(companyId.Value, customerId.Value, invoiceNo);

        //    // 优化3: 使用游标分页处理
        //    var result = new List<Guid>();
        //    var batchSize = 5000;  // 增大批次大小
        //    Guid? lastId = null;

        //    while (true)
        //    {
        //        // 优化4: 使用游标分页查询
        //        var batchQuery = baseQuery;
        //        if (lastId.HasValue)
        //        {
        //            batchQuery = batchQuery.Where(c => c.Id.CompareTo(lastId.Value) > 0);
        //        }

        //        var batch = await batchQuery
        //            .Take(batchSize)
        //            .ToListAsync();

        //        if (batch.Count == 0) break;

        //        lastId = batch.Last().Id;

        //        // 优化5: 使用更高效的内存过滤
        //        var validBatch = await ProcessBatchOptimized(
        //            batch,
        //            excludeBillCodes,
        //            excludeCreditIds
        //        );

        //        result.AddRange(validBatch);
        //    }

        //    return result;
        //}

        private async Task<(HashSet<string>, HashSet<Guid>)> GetExclusionSets(Guid companyId, Guid customerId, string? invoiceNo)
        {

            var excludeResults = new List<string>();

            excludeResults = await GetRecognizedBillCodes(companyId, customerId);
            excludeResults.AddRange(await GetAbatementBillCodes(companyId, customerId));
            excludeResults.AddRange(await GetNegativeReceivableBillCodes(companyId, customerId));
            var combinedExcludes = excludeResults.ToHashSet();



            // 条件5：发票关联排除
            var excludeCreditIds = invoiceNo != null
                ? await GetInvoicedCreditIds(companyId, customerId, invoiceNo)
                : new List<Guid>();

            return (combinedExcludes, excludeCreditIds.ToHashSet());
        }

        private async Task<List<string>> GetRecognizedBillCodes(Guid companyId, Guid customerId)
        {
            return await _db.RecognizeReceiveDetailCredits
                .Where(r => _db.Credits.Any(c =>
                    c.BillCode == r.CreditCode &&
                    c.CompanyId == companyId &&
                    c.CustomerId == customerId))
                .Select(r => r.CreditCode)
                .Distinct()
                .ToListAsync();
        }

        private async Task<List<string>> GetAbatementBillCodes(Guid companyId, Guid customerId)
        {
            return await _db.Abatements
                .Where(a => _db.Credits.Any(c =>
                    (c.BillCode == a.CreditBillCode || c.BillCode == a.DebtBillCode) &&
                    c.CompanyId == companyId &&
                    c.CustomerId == customerId))
                .Select(a => a.CreditType == "credit" ? a.CreditBillCode : a.DebtBillCode)
                .Distinct()
                .ToListAsync();
        }

        private async Task<List<string>> GetNegativeReceivableBillCodes(Guid companyId, Guid customerId)
        {
            return await _db.RecognizeReceiveItems
                .Where(r => r.Type == "负数应收" &&
                           r.Status != RecognizeReceiveItemStatusEnum.Canceled &&
                           _db.Credits.Any(c =>
                               c.BillCode == r.ReceiveCode &&
                               c.CompanyId == companyId &&
                               c.CustomerId == customerId))
                .Select(r => r.ReceiveCode)
                .Distinct()
                .ToListAsync();
        }

        private async Task<List<Guid>> GetInvoicedCreditIds(Guid companyId, Guid customerId, string invoiceNo)
        {
            return await _db.InvoiceCredits
                .Where(ic => ic.InvoiceNo == invoiceNo &&
                            ic.CreditId != null &&
                            _db.Credits.Any(c =>
                                c.Id == ic.CreditId &&
                                c.CompanyId == companyId &&
                                c.CustomerId == customerId))
                .Select(ic => ic.CreditId.Value)
                .ToListAsync();
        }

        private async Task<List<Guid>> ProcessBatchOptimized(
            List<CreditBatchDto> batch,
            HashSet<string> excludeBillCodes,
            HashSet<Guid> excludeCreditIds)
        {
            var batchIds = batch.Select(x => x.Id).ToHashSet();
            var batchBillCodes = batch.Select(x => x.BillCode).ToHashSet();

            // 条件1：优化后的复合条件查询
            var condition1Query = _db.Credits
                .Where(c => batchIds.Contains(c.Id))
                .GroupJoin(_db.CreditDetails,
                    credit => credit.Id,
                    detail => detail.CreditId,
                    (credit, details) => new { credit, details })
                .SelectMany(
                    x => x.details.DefaultIfEmpty(),
                    (x, detail) => new { x.credit, detail })
                .GroupBy(x => x.credit.Id)
                .Select(g => new
                {
                    CreditId = g.Key,
                    HasDetails = g.Any(x => x.detail != null),
                    AllMatch = g.All(x => x.detail == null || x.detail.NoInvoiceAmount == x.detail.Amount)
                })
                .Where(x => !x.HasDetails || x.AllMatch)
                .Select(x => x.CreditId);

            var ids1 = await condition1Query.ToListAsync();
            if (ids1.Count == 0) return new List<Guid>();

            // 合并所有过滤条件
            return batch
                .Where(b => ids1.ToHashSet().Contains(b.Id))  // 满足条件1
                .Where(b => !excludeBillCodes.Contains(b.BillCode))  // 排除条件2+3+4
                .Where(b => !excludeCreditIds.Contains(b.Id))  // 排除条件5
                .Select(b => b.Id)
                .ToList();
        }



        private class CreditBatchDto
        {
            public Guid Id { get; set; }
            public string BillCode { get; set; }
        }





        #endregion

        /// <summary>
        /// 根据OrderNo获取订单id
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> GetOrderId([FromBody] CreditQueryInput query)
        {
            var detailOfSale = await _sellApiClient.GetSaleByCode(query.OrderNo);
            if (detailOfSale != null)
            {
                return BaseResponseData<string>.Success(detailOfSale.Id);
            }
            return BaseResponseData<string>.Failed(500, $"未找到{query.OrderNo}的订单详情");
        }


        /// <summary>
        /// 客户应收明细
        /// </summary>
        /// <param name="customerId"></param>
        /// <param name="date">截止日期，不需要时分秒</param>
        /// <returns></returns>
        public async Task<List<CreditDetailsOutput>> GetCreditBalanceByCustomerId(Guid companyId, Guid customerId, System.DateTime date)
        {
            date = date.AddDays(1).AddSeconds(-1);
            var creditQuery = _db.Credits.Where(p => p.CompanyId == companyId && p.CustomerId == customerId && p.BillDate <= date).AsNoTracking();
            var creditCodes = creditQuery.Select(p => p.BillCode);//应收
            var abatementsQuery = _db.Abatements.Where(p => p.Abtdate <= date && (creditCodes.Contains(p.DebtBillCode) || creditCodes.Contains(p.CreditBillCode))).AsNoTracking();
            var credits = await creditQuery.ToListAsync();
            var abatements = await abatementsQuery.ToListAsync();
            var res = new List<CreditDetailsOutput>();
            foreach (var item in credits)
            {
                var abatement = abatements.Where(t => t.DebtBillCode == item.BillCode || t.CreditBillCode == item.BillCode).Sum(t => t.Value);
                if (Math.Abs(item.Value) == Math.Abs(abatement))
                {
                    continue;
                }
                res.Add(new CreditDetailsOutput()
                {
                    CreditNo = item.BillCode,
                    Amount = item.Value,
                    AbatmentAmount = abatement,
                    CreditDate = item.BillDate.Value
                });
            }
            return res;
        }

        /// <summary>
        /// 带发票明细
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="customerId"></param>
        /// <param name="date"></param>
        /// <returns></returns> 
        public async Task<List<CreditBalanceOutput>> GetCreditInvoiceBalanceByCustomerId(Guid companyId, Guid customerId, System.DateTime date)
        {
            date = date.AddDays(1).AddSeconds(-1);
            var invoiceQuery = _db.Invoices.Where(p => p.CompanyId == companyId && p.CustomerId.Equals(customerId.ToString()) && p.InvoiceTime <= date).AsNoTracking();
            var invoices = await invoiceQuery.ToListAsync();
            var invoiceNos = invoiceQuery.Select(p => p.InvoiceNo);
            var status = new List<RecognizeReceiveItemStatusEnum> {
                RecognizeReceiveItemStatusEnum.Completed,
                RecognizeReceiveItemStatusEnum.PartCanceled
            };
            var recognizeRece = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem)
                                         .Where(p => p.Type == 1 &&
                                                     p.RecognizeReceiveItem.ReceiveValue > 0 &&
                                                     invoiceNos.Contains(p.Code) &&
                                                     p.RecognizeReceiveItem.BillDate <= date &&
                                                     status.Contains(p.RecognizeReceiveItem.Status) &&
                                                     p.Status != RecognizeReceiveDetailEnum.Cancel
                                         ).AsNoTracking().ToListAsync();
            var res = new List<CreditBalanceOutput>();
            foreach (var invoice in invoices)
            {
                if (invoice.InvoiceAmount < 0)
                {
                    continue;
                }
                if (invoice.IsRedOff.HasValue && invoice.IsRedOff.Value)//如果红冲了
                {
                    if (invoice.InvoiceAmount == Math.Abs(invoice.RedAmount.Value))
                    {
                        continue;//全额红冲的发票不处理
                    }
                }
                var reconAmount = recognizeRece.Where(p => p.Code == invoice.InvoiceNo).Sum(t => t.Value);
                res.Add(new CreditBalanceOutput()
                {
                    InvoiceNo = invoice.InvoiceNo,
                    Amount = invoice.InvoiceAmount.Value,
                    InvoiceDate = invoice.InvoiceTime.Value,
                    ReceiveAmount = reconAmount + ((invoice.ReceiveAmount != null && invoice.IsInit == true) ? invoice.ReceiveAmount.Value : 0),
                });
            }
            return res;
        }
        /// <summary>
        /// 分批收入确认
        /// </summary>
        /// <param name="input"></param>
        public async Task<(List<CreditSureIncomeQueryOutput>, int)> GetPartialIncome(CreditSureIncomeQueryInput input)
        {
            if (!input.CreditId.HasValue)
            {
                return (new List<CreditSureIncomeQueryOutput>(), 0);
            }
            var list = await _db.CreditSureIncome.Where(x => x.CreditId == input.CreditId).Select(t => t.Adapt<CreditSureIncomeQueryOutput>()).AsNoTracking().ToListAsync();
            return (list, list.Count);
        }
        /// <summary>
        /// 导出应收清单任务
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditListExportOutput>, int)> CreditListExportAsync(CreditQueryInput query)
        {
            try
            {
                var (list, count) = await GetListAsync(query);
                var retList = new List<CreditListExportOutput>();
                var creditIds = list.Select(t => (Guid?)t.Id).ToList();
                var creditCodes = list.Select(t => t.BillCode).ToList();
                //var lossList = await _db.LossRecognitionDetails.Include(x=>x.LossRecognitionItem).Where(x =>x.LossRecognitionItem.Status == StatusEnum.Complate && creditCodes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                var abatementsList = await _db.Abatements.Where(p => (creditCodes.Contains(p.DebtBillCode) || creditCodes.Contains(p.CreditBillCode))).AsNoTracking().ToListAsync();
                var invoices = await _db.InvoiceCredits.Where(z => z.CreditId != null && creditIds.Contains(z.CreditId)).AsNoTracking().ToListAsync();
                var ciCodes = invoices.Select(x => x.CustomizeInvoiceCode).ToList();
                var ciItems = await _db.CustomizeInvoiceItem.Where(x => ciCodes.Contains(x.Code)).AsNoTracking().ToListAsync();

                foreach (var item in list)
                {

                    var abatementsInfo = abatementsList.Where(p => p.DebtBillCode == item.BillCode || p.CreditBillCode == item.BillCode).ToList();
                    var invoiceInfos = invoices.Where(z => z.CreditId != null && item.Id == z.CreditId).ToList();
                    var maxCount = abatementsInfo.Count > invoiceInfos.Count ? abatementsInfo.Count : invoiceInfos.Count;
                    if (maxCount == 0)
                    {
                        retList.Add(item.Adapt<CreditListExportOutput>());
                        retList.Last().IsSureIncome = item.IsSureIncome == 1 ? "是" : "否";
                        retList.Last().BillDate = item.BillDate.Value.ToString("yyyy-MM-dd");
                        retList.Last().InvoiceAmount = null;
                    }
                    for (int i = 0; i < maxCount; i++)//取他们之中多的
                    {

                        var creditItem = new CreditListExportOutput();

                        if (i == 0)
                        {
                            creditItem = item.Adapt<CreditListExportOutput>();
                            creditItem.IsSureIncome = item.IsSureIncome == 1 ? "是" : "否";
                            creditItem.BillDate = item.BillDate.Value.ToString("yyyy-MM-dd");
                        }
                        if (abatementsInfo != null && abatementsInfo.Count > i)
                        {
                            creditItem.ReceiveNo = item.BillCode == abatementsInfo[i].CreditBillCode ? abatementsInfo[i].DebtBillCode : abatementsInfo[i].CreditBillCode;
                            creditItem.AbatmentAmount = abatementsInfo[i].Value;
                        }
                        if (invoiceInfos != null && invoiceInfos.Count > i)
                        {
                            var single = ciItems.FirstOrDefault(x => x.Code == invoiceInfos[i].CustomizeInvoiceCode);
                            var changeStr = single != null ? single.ChangedStatus.GetDescription() : string.Empty;
                            if (string.IsNullOrEmpty(changeStr))
                            {
                                changeStr = "未红冲";
                            }
                            creditItem.InvoiceNo = invoiceInfos[i].InvoiceNo;
                            creditItem.InvoiceCode = invoiceInfos[i].InvoiceCode;
                            creditItem.InvoiceTime = invoiceInfos[i].InvoiceTime.Value.ToString("yyyy-MM-dd");
                            creditItem.InvoiceAmount = invoiceInfos[i].InvoiceAmount;
                            creditItem.CreditAmount = invoiceInfos[i].CreditAmount;
                            creditItem.ChangeStr = changeStr;
                            creditItem.InvoiceBlueNo = single != null ? single.InvoiceNo : string.Empty;
                            creditItem.IsCancel = invoiceInfos[i].IsCancel == true ? "已作废" : "已开票";
                        }
                        else
                        {
                            creditItem.InvoiceAmount = null;
                        }
                        creditItem.LossValue = item.LossRecognitionValue;
                        //creditItem.LossValue = lossList.Where(x => x.BillCode == item.BillCode).Sum(x => x.BadAmount);
                        retList.Add(creditItem);
                    }
                }
                retList.ForEach(p =>
                {
                    if (p.Value < 0 && p.LeftAmount > 0)
                    {
                        p.LeftAmount = -p.LeftAmount;
                    }
                });
                return (retList, count);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 销项发票导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceCreditExportListOutput>, int)> InvoiceCreditListExportAsync(InvoiceCreditQueryInput query)
        {
            var (list, count) = await GetInvoiceCreditListAsync(query);
            List<InvoiceCreditExportListOutput> InvoiceCreditExportListOutputlist = list.Adapt<List<InvoiceCreditExportListOutput>>();
            foreach (var item in InvoiceCreditExportListOutputlist)
            {
                item.CreatedTime = Convert.ToDateTime(item.CreatedTime).ToString("yyyy-MM-dd");
                item.InvoiceTime = Convert.ToDateTime(item.InvoiceTime).ToString("yyyy-MM-dd");
                item.NoTaxAmountPrice = Convert.ToDouble(item.TotalNoTaxAmountPrice).ToString();
                item.Price = Convert.ToDouble(item.Price).ToString();
                item.Quantity = Convert.ToDouble(item.Quantity).ToString();
            }
            return (InvoiceCreditExportListOutputlist, count);
        }

        /// <summary>
        /// 根据认款明细code和类型获取应收明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<DTOs.Recognize.CreditInfo>?> GetCreditInfoByRecognizeReceiveCode(RecognizeReceiveCreditInput input)
        {
            var retList = new List<DTOs.Recognize.CreditInfo>();
            // 认款类型 1=发票，2=订单，3=初始应收
            if (input.Type == 1)
            {
                retList = await (from ic in _db.InvoiceCredits
                                 join c in _db.Credits on ic.CreditId equals c.Id
                                 where input.Codes.ToHashSet().Contains(ic.InvoiceNo) && c.AbatedStatus != AbatedStatusEnum.Abated
                                 select new DTOs.Recognize.CreditInfo
                                 {
                                     CreditId = c.Id,
                                     RecognizeReceiveDetailCode = ic.InvoiceNo,
                                     BillCode = c.BillCode,
                                     CreditType = c.CreditType,
                                     BillDate = c.BillDate,
                                     OrderNo = c.OrderNo,
                                     ProjectCode = c.ProjectCode,
                                     ProjectId = c.ProjectId,
                                     ProjectName = c.ProjectName,
                                     ServiceId = c.ServiceId,
                                     ServiceName = c.ServiceName,
                                     Value = c.Value,
                                     AssociationValue = ic.CreditAmount,
                                     CurrentValue = 0
                                 }).ToListAsync();
                //计算应收可认款金额
                if (retList.Any())
                {
                    var creditIds = retList.Select(x => x.CreditId).ToList();
                    var creidtCodes = retList.Select(c => c.BillCode).ToList();
                    //获取冲销表的总额
                    var abas = new List<AbatementPo>();
                    //获取应收定损金额
                    var loss = new List<LossRecognitionDetailPo>();
                    if (creidtCodes != null && creidtCodes.Any())
                    {
                        abas = await _db.Abatements.Where(x => creidtCodes.ToHashSet().Contains(x.DebtBillCode) || creidtCodes.ToHashSet().Contains(x.CreditBillCode)).AsNoTracking().ToListAsync();
                        loss = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => x.LossRecognitionItem.Status != StatusEnum.Refuse && creidtCodes.ToHashSet().Contains(x.BillCode)).AsNoTracking().ToListAsync();
                    }
                    var invoiceNos = retList.Select(x => x.RecognizeReceiveDetailCode).ToList();
                    //根据应收获取认款记录
                    var rrdcsByCredit = await _db.RecognizeReceiveDetailCredits.Where(x => creditIds.Any(p => p == x.CreditId)).ToListAsync();
                    //根据发票获取认款记录
                    var rrdcsByInvoice = await _db.RecognizeReceiveDetailCredits.Where(x => invoiceNos.Any(p => p == x.InvoiceNo)).ToListAsync();
                    foreach (var item in retList)
                    {
                        //当前应收认款总额
                        var currentrrdcsByCreditValue = rrdcsByCredit.Where(x => x.CreditId == item.CreditId).Sum(x => x.CurrentValue);
                        //当前发票&应收认款金额
                        var currentrrdcsByInvoiceCreditValue = rrdcsByInvoice.Where(x => x.InvoiceNo == item.RecognizeReceiveDetailCode && x.CreditId == item.CreditId).Sum(x => x.CurrentValue);
                        //应收冲销金额
                        var cAbaValue = abas.Where(x => x.DebtBillCode == item.BillCode || x.CreditBillCode == item.BillCode).Sum(x => x.Value);
                        //应收认款金额（冲销表重复记录）
                        var rAbaValue = abas.Where(x => x.DebtBillCode == item.BillCode && (x.CreditType == "receive" || x.CreditType == "credit") && x.DebtType == "credit").Sum(x => x.Value);
                        //应收定损金额
                        var lossValue = loss.Where(x => x.BillCode == item.BillCode).Sum(x => x.BadAmount ?? x.LeftAmount);
                        //当前应收金额-应收已认款金额
                        var aValue = item.Value - currentrrdcsByCreditValue - (cAbaValue - rAbaValue) - lossValue;
                        //当前应收分配发票金额-发票&应收已认款金额
                        var bValue = item.AssociationValue - currentrrdcsByInvoiceCreditValue;
                        //对比，取小
                        item.SurplusValue = aValue > bValue ? bValue : aValue;
                        item.CurrentValue = item.SurplusValue;
                    }
                }
            }
            else if (input.Type == 2)
            {
                retList = await _db.Credits.Where(x => (input.Codes.ToHashSet().Contains(x.OrderNo)) && x.AbatedStatus != AbatedStatusEnum.Abated).Select(c => new DTOs.Recognize.CreditInfo
                {
                    CreditId = c.Id,
                    RecognizeReceiveDetailCode = c.OrderNo,
                    BillCode = c.BillCode,
                    CreditType = c.CreditType,
                    BillDate = c.BillDate,
                    OrderNo = c.OrderNo,
                    ProjectCode = c.ProjectCode,
                    ProjectId = c.ProjectId,
                    ProjectName = c.ProjectName,
                    ServiceId = c.ServiceId,
                    ServiceName = c.ServiceName,
                    Value = c.Value,
                    CurrentValue = 0
                }).ToListAsync();
                var retList2 = await _db.Credits.Where(x => (input.Codes.ToHashSet().Contains(x.RelateCode)) && x.AbatedStatus != AbatedStatusEnum.Abated).Select(c => new DTOs.Recognize.CreditInfo
                {
                    CreditId = c.Id,
                    RecognizeReceiveDetailCode = c.RelateCode,
                    BillCode = c.BillCode,
                    CreditType = c.CreditType,
                    BillDate = c.BillDate,
                    OrderNo = c.OrderNo,
                    ProjectCode = c.ProjectCode,
                    ProjectId = c.ProjectId,
                    ProjectName = c.ProjectName,
                    ServiceId = c.ServiceId,
                    ServiceName = c.ServiceName,
                    Value = c.Value,
                    CurrentValue = 0
                }).ToListAsync();
                retList.AddRange(retList2);
                //出重复
                retList = retList.DistinctBy(p => p.CreditId).ToList();
                //计算应收可认款金额
                if (retList.Any())
                {
                    var creditIds = retList.Select(x => x.CreditId).ToList();
                    var creidtCodes = retList.Select(c => c.BillCode).ToList();
                    //获取冲销表的总额
                    var abas = new List<AbatementPo>();
                    //获取应收定损金额
                    var loss = new List<LossRecognitionDetailPo>();
                    if (creidtCodes != null && creidtCodes.Any())
                    {
                        abas = await _db.Abatements.Where(x => creidtCodes.ToHashSet().Contains(x.DebtBillCode) || creidtCodes.ToHashSet().Contains(x.CreditBillCode)).AsNoTracking().ToListAsync();
                        loss = await _db.LossRecognitionDetails.Include(x => x.LossRecognitionItem).Where(x => x.LossRecognitionItem.Status != StatusEnum.Refuse && creidtCodes.ToHashSet().Contains(x.BillCode)).AsNoTracking().ToListAsync();
                    }
                    //根据应收获取认款记录
                    var rrdcsByCredit = await _db.RecognizeReceiveDetailCredits.Where(x => creditIds.Any(p => p == x.CreditId)).ToListAsync();
                    foreach (var item in retList)
                    {
                        //当前应收认款总额
                        var currentrrdcsByCreditValue = rrdcsByCredit.Where(x => x.CreditId == item.CreditId).Sum(x => x.CurrentValue);
                        //应收冲销金额
                        var cAbaValue = abas.Where(x => x.DebtBillCode == item.BillCode || x.CreditBillCode == item.BillCode).Sum(x => x.Value);
                        //应收认款金额（冲销表重复记录）
                        var rAbaValue = abas.Where(x => x.DebtBillCode == item.BillCode && (x.CreditType == "receive" || x.CreditType == "credit") && x.DebtType == "credit").Sum(x => x.Value);
                        //应收定损金额
                        var lossValue = loss.Where(x => x.BillCode == item.BillCode).Sum(x => x.BadAmount ?? x.LeftAmount);
                        //当前应收金额-应收已认款金额
                        item.SurplusValue = item.Value - currentrrdcsByCreditValue - (cAbaValue - rAbaValue) - lossValue;
                        item.CurrentValue = item.SurplusValue;
                    }
                }
            }
            return retList.Where(x => x.SurplusValue > 0).ToList();
        }
        /// <summary>
        /// 应收单列表查询，损失确认单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CreditQueryListOutput>, int)> GetListByLossRecognitionAsync(CreditQueryInput query)
        {
            Expression<Func<CreditPo, bool>> exp = z => 1 == 1;
            var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(input);
            #region 查询条件
            exp = await InitExp(query, exp, strategry);
            #endregion
            var recognizeItemIds = await _db.RecognizeReceiveItems.Where(p => p.Status != RecognizeReceiveItemStatusEnum.Completed && p.Status != RecognizeReceiveItemStatusEnum.Canceled).Select(p => p.Id).ToListAsync();
            var creditIds = await _db.RecognizeReceiveDetailCredits.Where(p => recognizeItemIds.ToHashSet().Contains(p.RecognizeReceiveItemId)).Select(p => p.CreditId).ToListAsync();
            if (creditIds.Count > 0)
            {
                exp = exp.And(p => !creditIds.ToHashSet().Contains(p.Id));
            }
            var lossCredits = await _db.LossRecognitionDetails.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Credit).AsNoTracking().ToListAsync();
            var lossItems = await _db.LossRecognitionItem.Where(p => lossCredits.Select(p => p.LossRecognitionItemId).ToHashSet().Contains(p.Id) && p.Status != StatusEnum.Complate && p.Status != StatusEnum.Refuse).ToListAsync();
            for (int i = 0; i < lossCredits.Count; i++)
            {
                if (lossItems.Where(p => p.Id == lossCredits[i].LossRecognitionItemId).Count() == 0)
                {
                    lossCredits.RemoveAt(i);
                    i--;
                }
            }
            if (lossCredits.Count > 0)
            {
                exp = exp.And(p => !lossCredits.Select(p => p.BillCode).ToHashSet().Contains(p.BillCode));
            }
            IQueryable<CreditPo> baseQuery = _db.Credits.Where(exp).AsNoTracking();
            //运营制作未开票
            if (!string.IsNullOrWhiteSpace(query.isNotInvoice) && query.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed)
            {
                baseQuery = baseQuery.Where(p => p.InvoiceStatus != InvoiceStatusEnum.invoiced);
                if (query.isContainZero != 1)
                {
                    baseQuery = baseQuery.Where(p => p.Value != 0);
                }
            }
            #region 排序
            if (query.sort != null && query.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<CreditPo>(query.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<CreditQueryListOutput>()).ToListAsync();
            var recognizeAmounts = await _recognizeReceiveAppService.GetRecognizeReceiveAmount(list.Select(p => p.BillCode).ToList(), 3);
            for (int i = 0; i < list.Count; i++)
            {
                list[i].CreditId = list[i].Id;
                list[i].AbatmentAmount = list[i].Value - recognizeAmounts.Where(p => p.CreditCode == list[i].BillCode).FirstOrDefault().CreditSurplusTotalValue.Value;
            }

            return (list, count);
        }

        /// <summary>
        /// 获取未开票原始应收明细列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<CreditDetailOutput>, int)> GetCreditDetailsAsync(CreditDetailsInput input)
        {
            Expression<Func<CreditDetailPo, bool>> exp = z => 1 == 1;

            #region 初始化数据策略条件
            var strategyInput = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);

            if (strategry != null)
            {
                var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    exp = exp.And(z => 1 != 1);
                }
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                    {
                        exp = exp.And(z => 1 != 1);
                    }
                    else
                    {
                        foreach (var key in strategry.RowStrategies.Keys)
                        {
                            if (key.ToLower() == "company")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                                    exp = exp.And(t => strategList.Contains(t.Credit.CompanyId.ToString()));
                                }
                            }
                            if (key.ToLower() == "service")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                                    exp = exp.And(t => strategList.Contains(t.Credit.ServiceId.ToString()));
                                }
                            }
                            else if (key.ToLower() == "project")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                                    exp = exp.And(t => !t.Credit.ProjectId.HasValue || strategList.Contains(t.Credit.ProjectId.ToString()));
                                }
                            }
                            if (key.ToLower() == "accountingdept")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                                    exp = exp.And(t => strategList.Contains(t.Credit.BusinessDeptId));
                                }
                            }
                            if (key.ToLower() == "customer")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].Select(z => Guid.Parse(z)).ToHashSet();
                                    exp = exp.And(t => strategList.Contains(t.Credit.CustomerId.Value));
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            #region 查询条件拼接

            if (input.CompanyId.HasValue)
            {
                exp = exp.And(cd => cd.Credit != null && cd.Credit.CompanyId == input.CompanyId.Value);
            }
            if (input.CustomerIds != null && input.CustomerIds.Any())
            {
                exp = exp.And(z => z.Credit.CustomerId.HasValue && input.CustomerIds.ToHashSet().Contains(z.Credit.CustomerId.Value));
            }

            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                exp = exp.And(cd => cd.Credit != null && EF.Functions.Like(cd.Credit.OrderNo, $"%{input.OrderNo}%"));
            }

            if (!string.IsNullOrEmpty(input.RelateCode))
            {
                exp = exp.And(cd => cd.Credit != null && EF.Functions.Like(cd.Credit.RelateCode, $"%{input.RelateCode}%"));
            }
            if (!string.IsNullOrWhiteSpace(input.OriginOrderNo))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.Credit.OriginOrderNo) && z.Credit.OriginOrderNo.Equals(input.OriginOrderNo));
            }

            if (!string.IsNullOrEmpty(input.BillCode))
            {
                exp = exp.And(cd => cd.Credit != null && EF.Functions.Like(cd.Credit.BillCode, $"%{input.BillCode}%"));
            }
            if (!string.IsNullOrWhiteSpace(input.SaleSystemName))
            {
                exp = exp.And(z => z.Credit.SaleSystemName.Contains(input.SaleSystemName));
            }
            if (!string.IsNullOrWhiteSpace(input.ProductNo))
            {
                exp = exp.And(z => z.ProductNo.Contains(input.ProductNo));
            }
            if (!string.IsNullOrWhiteSpace(input.ProjectName))
            {
                exp = exp.And(z => z.Credit.ProjectName.Contains(input.ProjectName));
            }
            if (!string.IsNullOrWhiteSpace(input.ProductName))
            {
                exp = exp.And(z => z.ProductName.Contains(input.ProductName));
            }
            if (!string.IsNullOrEmpty(input.searchKey))
            {
                exp = exp.And(cd => cd.ProductName.Contains(input.searchKey) ||
                                    cd.ProductNo.Contains(input.searchKey));
            }
            // 应收类型多选
            if (input.CreditTypes != null && input.CreditTypes.Any())
            {
                exp = exp.And(z => input.CreditTypes.Contains(((int)z.Credit.CreditType).ToString()));
            }
            if (input.BillDateStart.HasValue && input.BillDateEnd.HasValue)
            {
                exp = exp.And(cd => cd.Credit != null && cd.Credit.BillDate >= input.BillDateStart.Value &&
                                    cd.Credit.BillDate <= input.BillDateEnd.Value);
            }
            var creditDetailIds = await _db.CreditInvoiceDetailTemps.Where(p => p.CreatedBy == input.CurrentUserName).Select(p => p.CreditDetailId).ToListAsync();
            if (creditDetailIds.Any())
            {
                exp = exp.And(cd => !creditDetailIds.Contains(cd.Id));
            }
            if (input.isContainZero != 1)
            {
                exp = exp.And(p => p.NoInvoiceAmount != 0);
            }
            else
            {
                exp = exp.And(p => (p.NoInvoiceAmount == 0 && p.Amount == 0) || p.NoInvoiceAmount != 0);
            }
            if (!string.IsNullOrEmpty(input.Note))
            {
                exp = exp.And(z => EF.Functions.Like(z.Credit.Note, $"%{input.Note}%"));
            }
            if (input.CreditSaleSubType.HasValue)
            {
                exp = exp.And(z => z.Credit.CreditSaleSubType == input.CreditSaleSubType);
            }
            if (!string.IsNullOrWhiteSpace(input.ShipmentCode))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.Credit.ShipmentCode) && z.Credit.ShipmentCode.Equals(input.ShipmentCode));
                //exp = exp.And(z => EF.Functions.Like(z.Credit.ShipmentCode, $"%{input.ShipmentCode}%"));
            }
            if (!string.IsNullOrWhiteSpace(input.RedReversalConsumNo))
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.Credit.RedReversalConsumNo) && z.Credit.RedReversalConsumNo.Equals(input.RedReversalConsumNo));
                //exp = exp.And(z => EF.Functions.Like(z.Credit.RedReversalConsumNo, $"%{input.RedReversalConsumNo}%"));
            }
            if (!string.IsNullOrEmpty(input.CustomerOrderCode))
            {
                //exp = exp.And(z => EF.Functions.Like(z.Credit.CustomerOrderCode, $"%{input.CustomerOrderCode}%"));
                exp = exp.And(z => !string.IsNullOrEmpty(z.Credit.CustomerOrderCode) && z.Credit.CustomerOrderCode.Equals(input.CustomerOrderCode));
            }
            exp = exp.And(cd => cd.Credit.InvoiceStatus != InvoiceStatusEnum.invoiced &&
                                cd.Credit.IsNoNeedInvoice != IsNoNeedInvoiceEnum.NoNeed);

            #endregion


            IQueryable<CreditDetailPo> baseQuery = _db.CreditDetails
                .Include(cd => cd.Credit)
                .Where(exp)
                .AsNoTracking();

            #region 排序
            baseQuery = baseQuery.OrderByDescending(z => z.Credit.BillDate).ThenBy(p => p.Credit.BillCode);
            #endregion

            #region 分页统计
            var count = await baseQuery.CountAsync();
            var detailList = await baseQuery
                .Skip((input.page - 1) * input.limit)
                .Take(input.limit)
                .ToListAsync();
            #endregion

            #region 映射输出
            var outputList = detailList.Select(cd => new CreditDetailOutput
            {
                Id = cd.Id,
                OriginDetailId = cd.OriginDetailId,
                CompanyId = cd.Credit?.CompanyId,
                CompanyName = cd.Credit?.CompanyName,
                NameCode = cd.Credit?.NameCode,
                CustomerId = cd.Credit?.CustomerId.ToString(),
                CustomerName = cd.Credit?.CustomerName,
                ProductNo = cd.ProductNo,
                ProductName = cd.ProductName,
                Quantity = cd.Quantity,
                Price = cd.Price,
                Value = cd.Amount.Value,
                PurchaseCost = cd.OriginalCost,
                CreditBillCode = cd.Credit?.BillCode,
                RelateCode = cd.Credit?.RelateCode,
                OrderNo = cd.Credit?.OrderNo,
                OriginOrderNo = cd.Credit?.OriginOrderNo,
                ShipmentCode = cd.Credit?.ShipmentCode,
                RedReversalConsumNo = cd.Credit?.RedReversalConsumNo,
                DeptName = cd.Credit?.DeptName,
                ServiceId = cd.Credit?.ServiceId,
                ServiceName = cd.Credit?.ServiceName,
                ProjectId = cd.Credit?.ProjectId,
                ProjectName = cd.Credit?.ProjectName,
                CustomerOrderCode = cd.Credit?.CustomerOrderCode,
                CustomerPersonName = cd.Credit?.CustomerPersonName,
                SaleSystemId = cd.Credit?.SaleSystemId,
                SaleSystemName = cd.Credit?.SaleSystemName,
                CreditType = cd.Credit?.CreditType,
                CreditSaleSubType = cd.Credit?.CreditSaleSubType,
                Note = cd.Credit?.Note,
                BusinessDeptFullName = cd.Credit?.BusinessDeptFullName,
                BusinessDeptId = cd.Credit?.BusinessDeptId,
                CreatedBy = cd.Credit?.CreatedBy,
                NoInvoiceAmount = cd.NoInvoiceAmount,
                NoInvoiceAmountShow = cd.NoInvoiceAmount,
                InvoiceAmount = cd.InvoiceAmount,
                BillDate = cd.Credit?.BillDate,

                OriginalPackSpec = cd.PackSpec,
                OriginProductName = cd.ProductName,
                PackUnit = cd.PackUnit,
                OriginPackUnit = cd.PackUnit,
                Specification = cd.Specification,
                OriginalPrice = cd.OriginalPrice,
                OriginalCost = cd.OriginalCost,
                TaxRate = cd.TaxRate,
                CreditDetailId = cd.Id,
                OriginalId = cd.OriginalId,
                ProductId = cd.ProductId,
                AgentId = cd.AgentId,
                IFHighValue = cd.IFHighValue,
                BatchId = cd.BatchId,
                SaleDetailId = cd.SaleDetailId,
                PriceSource = cd.PriceSource,

            }).ToList();
            #endregion

            return (outputList, count);
        }
        /// <summary>
        /// 插入应收开票明细临时表
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> AddCreditInvoiceDetailTemps(List<CreditDetailOutput> inputs, string currentUserName)
        {
            //校验inputs中客户不能为空
            if (inputs == null || !inputs.Any())
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：未找到数据"
                };
            }
            var noValueinputs = inputs.Where(p => !p.NoInvoiceAmount.HasValue).ToList();
            if (noValueinputs.Any())
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：本次开票金额不能为空"
                };
            }
            var zoreValueinputs = inputs.Where(p => p.NoInvoiceAmount == 0 && p.Value != 0).ToList();
            if (zoreValueinputs.Any())
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：本次开票金额不能为0"
                };
            }
            //inputs都必须是同一客户,否则返回失败 
            var customerId = inputs.FirstOrDefault()?.CustomerId;
            if (inputs.Any(x => x.CustomerId != customerId))
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：请选择同一客户"
                };
            }
            //inputs都必须是同一公司,否则返回失败 
            var companyId = inputs.FirstOrDefault()?.CompanyId;
            if (inputs.Any(x => x.CompanyId != companyId))
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：请选择同一公司"
                };
            }
            //inputs都必须是同一公司,否则返回失败 
            var creditSaleSubType = inputs.FirstOrDefault()?.CreditSaleSubType;
            if (inputs.Any(x => x.CreditSaleSubType != creditSaleSubType))
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：个人消费类型和平台不能同时操作"
                };
            }
            //1,根据当前用户名称查询CreditInvoiceDetailTemp列表数据
            var creditInvoiceDetailTempList = await _db.CreditInvoiceDetailTemps.Include(p => p.CreditDetail).ThenInclude(p => p.Credit)
                .Where(x => x.CreatedBy == currentUserName)
                .ToListAsync();
            //2,如果creditInvoiceDetailTempList存在。如果inputs中的客户和creditInvoiceDetailTempLis中不一致，则提示客户名称不一致
            if (creditInvoiceDetailTempList.Any())
            {
                var creditInvoiceDetailTemp = creditInvoiceDetailTempList.FirstOrDefault();
                if (creditInvoiceDetailTemp != null)
                {
                    if (creditInvoiceDetailTemp.CustomerId != customerId)
                    {
                        return new BaseResponseData<string>
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "操作失败，原因：勾选的应收明细的客户需要和开票明细的客户保持一致"
                        };
                    }
                    if (creditInvoiceDetailTemp.CompanyId != companyId)
                    {
                        return new BaseResponseData<string>
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "操作失败，原因：勾选的应收明细的公司需要和开票明细的公司保持一致"
                        };
                    }
                    if (creditInvoiceDetailTemp.CreditDetail.Credit.CreditSaleSubType != creditSaleSubType)
                    {
                        return new BaseResponseData<string>
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "操作失败，原因：勾选的应收明细的应收对象需要和开票明细的应收对象保持一致"
                        };
                    }
                }
            }
            //校验inputs中noinvoiceAmount是否为空
            if (inputs.Any(x => x.NoInvoiceAmount == null))
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "操作失败，原因：请填写未开票金额"
                };
            }
            // 校验inputs中noinvoiceAmount不能大于数据库CreditDetails中的noinvoiceAmount
            var creditDetailIds = inputs.Select(i => i.CreditDetailId).ToList();

            // 查询数据库中对应的明细记录
            var dbCreditDetails = await _db.CreditDetails
                .Where(cd => creditDetailIds.Contains(cd.Id))
                .ToDictionaryAsync(cd => cd.Id, cd => cd);
            string errorMessage = string.Empty;
            foreach (var input in inputs)
            {
                if (!dbCreditDetails.TryGetValue(input.CreditDetailId, out var dbDetail))
                {
                    return new BaseResponseData<string>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"明细ID {input.CreditDetailId} 不存在；"
                    };
                }
                if ((dbDetail.NoInvoiceAmount.Value > 0 && input.NoInvoiceAmount.Value < 0)|| (dbDetail.NoInvoiceAmount.Value < 0 && input.NoInvoiceAmount.Value > 0))
                {
                    errorMessage += $"应收单号{input.CreditBillCode},货号：{input.ProductNo} 的未开票金额：{dbDetail.NoInvoiceAmount.Value}，请输入合法的开票金额；";
                }
                if (Math.Abs(input.NoInvoiceAmount.Value) > Math.Abs(dbDetail.NoInvoiceAmount.Value))
                {
                    errorMessage += $"应收单号{input.CreditBillCode},货号：{input.ProductNo} 的开票金额输入值(绝对值){Math.Abs(input.NoInvoiceAmount.Value)} 超过最大允许值(绝对值) {Math.Abs(dbDetail.NoInvoiceAmount.Value)}；";
                }
            }
            if (!string.IsNullOrEmpty(errorMessage))
            {
                return new BaseResponseData<string>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = errorMessage
                };
            }
            var originDetailIds = inputs.Select(x => x.OriginDetailId).ToHashSet();
            if (originDetailIds.Any())
            {
                //CreditInvoiceDetailTempPo表中创建人有数据，则提示错误
                var creditInvoiceDetailTempsDB = await _db.CreditInvoiceDetailTemps.Where(p => originDetailIds.Contains(p.OriginDetailId) && p.CreatedBy == currentUserName).ToListAsync();
                if (creditInvoiceDetailTempsDB.Any())
                {
                    return new BaseResponseData<string>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "操作失败，原因：开票明细表中已经存在您要加入明细数据"
                    };
                }
            }
            if (creditDetailIds.Any())
            {
                //CreditInvoiceDetailTempPo表中创建人有数据，则提示错误
                var creditInvoiceDetailTempsDB = await _db.CreditInvoiceDetailTemps.Where(p => creditDetailIds.Contains(p.CreditDetailId.Value) && p.CreatedBy == currentUserName).ToListAsync();
                if (creditInvoiceDetailTempsDB.Any())
                {
                    return new BaseResponseData<string>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "操作失败，原因：开票明细表中已经存在您要加入明细数据"
                    };
                }
            }
            #region 销售开票名称
            var productIds = inputs.Select(p => p.ProductId.Value).ToList();
            var saleCustomerProducts = new List<SelectProductInvoicesOutput>();
            if (productIds.Any())
            {
                var selectProductInvoicesInputData = new List<SelectProductInvoicesInputData>();
                foreach (var productId in productIds)
                {
                    selectProductInvoicesInputData.Add(new SelectProductInvoicesInputData
                    {
                        ProductId = productId,
                        CustomerId = Guid.Parse(customerId),
                    });
                }
                saleCustomerProducts = await _bDSApiClient.SelectProductInvoices(new SelectProductInvoicesInput
                {
                    CompanyId = companyId,
                    List = selectProductInvoicesInputData
                });
            }
            #endregion
            #region 原始包装规格
            var productNos = inputs.Where(p => !string.IsNullOrEmpty(p.ProductNo)).Select(p => p.ProductNo).Distinct().ToList();
            var products = await _bDSApiClient.GetByNos(productNos, companyId.ToString());
            #endregion
            //将inputs写入CreditInvoiceDetailTemp表
            var temps = new List<CreditInvoiceDetailTempPo>();

            foreach (var item in inputs)
            {
                string specification = string.Empty;
                SelectProductInvoicesOutput customerProduct = null;
                var product = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                if (saleCustomerProducts != null && saleCustomerProducts.Any())
                {
                    customerProduct = saleCustomerProducts.FirstOrDefault(p => p.ProductId == item.ProductId);
                }
                if (customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec))
                {
                    //获取公司的开票规格默认值
                    specification = await Utility.GetCompanySpecificationAsync(product, companyId.ToString(), item.ProductNo, _bDSApiClient);
                }
                item.OriginSpecification = item.ProductNo;
                item.ProductName = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceName) ? (product != null && !string.IsNullOrEmpty(product.productName) ? product.productName : item.ProductName) : customerProduct.InvoiceName;
                item.PackUnit = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceUnit) ? (product != null ? product.packUnitDesc : item.PackUnit) : customerProduct.InvoiceUnit;
                item.Specification = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec) ? specification : customerProduct.InvoiceSpec;
                if (item.NoInvoiceAmount.HasValue && item.NoInvoiceAmount != 0)
                {
                    item.Quantity = item.NoInvoiceAmount.Value / item.Price;
                    item.Value = item.NoInvoiceAmount.Value;
                }
                var creditInvoiceDetailTempPo = item.Adapt<CreditInvoiceDetailTempPo>();
                creditInvoiceDetailTempPo.Id = Guid.NewGuid();
                creditInvoiceDetailTempPo.CreatedBy = currentUserName;
                temps.Add(creditInvoiceDetailTempPo);
            }

            await _db.CreditInvoiceDetailTemps.AddRangeAsync(temps);
            await _db.SaveChangesAsync();
            return new BaseResponseData<string>
            {
                Code = CodeStatusEnum.Success,
                Message = "操作成功！"
            };
        }

        /// <summary>
        /// 根据用户名获取 CreditInvoiceDetailTemp 记录
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <returns>符合条件的应收明细临时记录列表</returns>
        public async Task<List<OriginDetailOutput>> GetCreditInvoiceDetailTempsByUserNameAsync(string userName)
        {
            if (string.IsNullOrWhiteSpace(userName))
                throw new ArgumentException("用户名不能为空", nameof(userName));

            var result = await _db.CreditInvoiceDetailTemps
                .Where(t => t.CreatedBy == userName)
                .Select(p => p.Adapt<OriginDetailOutput>())
                .AsNoTracking()
                .ToListAsync();
            return result;
        }

        /// <summary>
        /// 根据CreditDetailIds删除CreditInvoiceDetailTemp记录
        /// </summary>
        /// <param name="creditDetailIds">应收明细ID列表</param>
        /// <returns>操作结果（成功/失败）</returns>
        public async Task<BaseResponseData<bool>> DeleteCreditInvoiceDetailTempsByCreditDetailIdsAsync(List<Guid> creditDetailIds)
        {
            var response = BaseResponseData<bool>.Success("删除成功");

            if (creditDetailIds == null || !creditDetailIds.Any())
            {
                response = BaseResponseData<bool>.Failed(500, "操作失败，原因：参数不能为空");
                return response;
            }

            try
            {
                // 查询需要删除的记录
                var recordsToDelete = await _db.CreditInvoiceDetailTemps
                    .Where(temp => creditDetailIds.Contains(temp.CreditDetailId.Value))
                    .ToListAsync();

                if (recordsToDelete != null && recordsToDelete.Any())
                {
                    // 从数据库上下文中移除记录
                    _db.CreditInvoiceDetailTemps.RemoveRange(recordsToDelete);

                    // 提交更改
                    await _db.SaveChangesAsync();
                }

                return response;
            }
            catch (Exception ex)
            {
                // 处理异常并返回错误信息
                response = BaseResponseData<bool>.Failed(500, $"删除记录时发生异常：{ex.Message}");
                return response;
            }
        }

        /// <summary>
        /// 根据CreditDetailIds删除CreditInvoiceDetailTemp记录
        /// </summary>
        /// <param name="originDetailIds">应收明细ID列表</param>
        /// <returns>操作结果（成功/失败）</returns>
        public async Task<BaseResponseData<bool>> DeleteCreditInvoiceDetailTempsByOriginDetailIdsAsync(List<string> originDetailIds)
        {
            var response = BaseResponseData<bool>.Success("删除成功");

            if (originDetailIds == null || !originDetailIds.Any())
            {
                response = BaseResponseData<bool>.Failed(500, "操作失败，原因：参数不能为空");
                return response;
            }

            try
            {
                // 查询需要删除的记录
                var recordsToDelete = await _db.CreditInvoiceDetailTemps
                    .Where(temp => originDetailIds.Contains(temp.OriginDetailId))
                    .ToListAsync();

                if (recordsToDelete != null && recordsToDelete.Any())
                {
                    // 从数据库上下文中移除记录
                    _db.CreditInvoiceDetailTemps.RemoveRange(recordsToDelete);

                    // 提交更改
                    await _db.SaveChangesAsync();
                }

                return response;
            }
            catch (Exception ex)
            {
                // 处理异常并返回错误信息
                response = BaseResponseData<bool>.Failed(500, $"删除记录时发生异常：{ex.Message}");
                return response;
            }
        }
    }
}
