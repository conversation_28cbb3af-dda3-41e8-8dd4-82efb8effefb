using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Inno.CorePlatform.Finance.Domain.Enums;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.Logging;
using Inno.CorePlatform.Common.Utility.Strings;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices
{
    /// <summary>
    /// 自动盘点服务实现
    /// </summary>
    public class AutoInventoryService : IAutoInventoryService
    {
        #region 内部数据结构

        /// <summary>
        /// 公司盘点信息
        /// </summary>
        private class CompanyInventoryInfo
        {
            public Guid CompanyId { get; set; }
            public string CompanyName { get; set; } = string.Empty;
            public Guid InventoryId { get; set; }
            public int CurrentStatus { get; set; }
            public bool NeedsStatusUpdate { get; set; }
        }

        /// <summary>
        /// 盘点完成检查结果
        /// </summary>
        private class InventoryCompletionCheckResult
        {
            public List<CompanyInventoryInfo> QualifiedCompanies { get; set; } = new();
            public List<(Guid CompanyId, string CompanyName, string Reason)> FailedCompanies { get; set; } = new();
        }

        /// <summary>
        /// 系统月度更新结果
        /// </summary>
        private class SystemMonthUpdateResult
        {
            public List<CompanyInventoryInfo> SuccessfulCompanies { get; set; } = new();
            public List<(Guid CompanyId, string CompanyName, string ErrorMessage)> FailedCompanies { get; set; } = new();
            public string NewSystemPeriod { get; set; } = string.Empty;
        }

        /// <summary>
        /// 盘点状态更新结果
        /// </summary>
        private class InventoryStatusUpdateResult
        {
            public List<CompanyInventoryInfo> SuccessfulCompanies { get; set; } = new();
            public List<(Guid CompanyId, string CompanyName, string ErrorMessage)> FailedCompanies { get; set; } = new();
        }

        /// <summary>
        /// 通知发布结果
        /// </summary>
        private class NotificationPublishResult
        {
            public bool Success { get; set; }
            public string Message { get; set; } = string.Empty;
            public List<Guid> NotifiedCompanyIds { get; set; } = new();
        }

        #endregion

        #region 依赖注入字段

        private readonly ILogger<AutoInventoryService> _logger;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly IInventoryRecordRepository _inventoryRecordRepository;
        private readonly IInventoryRecordQueryService _inventoryRecordQueryService;
        private readonly IApplyBFFService _applyBFFService;
        private readonly IInventoryRepository _inventoryRepository;
        private readonly IRebateProvisionItemRepository _rebateProvisionItemRepository;
        private readonly IRebateProvisionQueryService _rebateProvisionQueryService;
        private readonly DaprClient _daprClient;
        private readonly IBDSApiClient _bDSApiClient;

        public AutoInventoryService(
            ILogger<AutoInventoryService> logger,
            IInventoryMgmAppService inventoryMgmAppService,
            IInventoryRecordRepository inventoryRecordRepository,
            IInventoryRecordQueryService inventoryRecordQueryService,
            IApplyBFFService applyBFFService,
            IInventoryRepository inventoryRepository,
            IRebateProvisionItemRepository rebateProvisionItemRepository,
            IRebateProvisionQueryService rebateProvisionQueryService,
            DaprClient daprClient,
            IBDSApiClient bDSApiClient)
        {
            _logger = logger;
            _inventoryMgmAppService = inventoryMgmAppService;
            _inventoryRecordRepository = inventoryRecordRepository;
            _inventoryRecordQueryService = inventoryRecordQueryService;
            _applyBFFService = applyBFFService;
            _inventoryRepository = inventoryRepository;
            _rebateProvisionItemRepository = rebateProvisionItemRepository;
            _rebateProvisionQueryService = rebateProvisionQueryService;
            _daprClient = daprClient;
            _bDSApiClient = bDSApiClient;
        }

        /// <summary>
        /// 处理开启盘点事件
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message, List<Guid> ProcessedCompanyIds)> ProcessStartInventoryEventAsync(StartInventoryEventDto eventDto)
        {
            try
            {
                _logger.LogInformation("开始盘点 - 接收事件 - 系统月度: {SysMonth}", eventDto.SysMonth);

                // 创建子公司盘点记录并获取创建的记录信息
                var (success, message, createdInventoryItems) = await CreateCompanyInventoryRecordsAsync(eventDto.SysMonth);
                if (!success)
                {
                    if (!createdInventoryItems.Any())
                    {
                        _logger.LogWarning("开始盘点 - 创建子公司记录-失败 - 系统月度: {SysMonth}, 原因: {Message}", eventDto.SysMonth, message);
                        return (false, message, new List<Guid>());
                    }
                }

                var processedCompanyIds = createdInventoryItems.Select(item => item.CompanyId).ToList();
                _logger.LogInformation("开始盘点 - 创建子公司记录-成功 - 系统月度: {SysMonth}, 公司数量: {Count}", eventDto.SysMonth, processedCompanyIds.Count);

                // 处理财务内部盘点记录生成（在发送广播前完成）
                var internalProcessResult = await ProcessFinanceInternalInventoryForAllCompaniesAsync(createdInventoryItems, eventDto.SysMonth);
                if (!internalProcessResult.Success)
                {
                    _logger.LogError("开始盘点 - 财务内部盘点失败 - 系统月度: {SysMonth}, 错误: {Message}", eventDto.SysMonth, internalProcessResult.Message);
                    // 可以选择继续或者终止，这里选择记录错误但继续流程
                }

                // 发布盘点记录生成事件（只处理外部能力中心业务）
                var generateEvent = new InventoryGenerateEventDto
                {
                    CompanyIds = processedCompanyIds,
                    SysMonth = eventDto.SysMonth
                };
                _logger.LogInformation("开始盘点 - 发布外部盘点事件 - Topic: finance-inventory-generate, 公司数量: {Count}", processedCompanyIds.Count);
                await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, "finance-inventory-generate", generateEvent);
                _logger.LogInformation("开始盘点 - 发布外部盘点事件成功 - Topic: finance-inventory-generate, 公司数量: {Count},Input:{generateEvent}", processedCompanyIds.Count, generateEvent?.ToJson());

                // 处理返利计提状态并推送到金蝶（在开始盘点时处理）
                await ProcessRebateProvisionForAllCompaniesAsync(processedCompanyIds, eventDto.SysMonth);

                _logger.LogInformation("开始盘点 - 处理完成 - 系统月度: {SysMonth}, 成功处理公司数量: {Count}", eventDto.SysMonth, processedCompanyIds.Count);
                return (internalProcessResult.Success, internalProcessResult.Success ? "开启盘点成功" : internalProcessResult.Message, processedCompanyIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始盘点 - 处理异常 - 系统月度: {SysMonth}, 错误: {Message}", eventDto.SysMonth, ex.Message);
                return (false, $"处理失败: {ex.Message}", new List<Guid>());
            }
        }

        /// <summary>
        /// 创建子公司盘点记录（公共方法）
        /// </summary>
        /// <param name="sysMonth">系统月度</param>
        /// <returns>成功标志、消息、创建的盘点记录列表</returns>
        public async Task<(bool Success, string Message, List<InventoryItem> CreatedInventoryItems)> CreateCompanyInventoryRecordsAsync(string sysMonth)
        {
            try
            {
                _logger.LogInformation("开始盘点 - 创建子公司记录 - 系统月度: {SysMonth}", sysMonth);

                // 首先检查当前月度是否已有盘点记录
                var existingInventories = await _inventoryRepository.GetInventoriesBySysMonth(sysMonth);
                if (existingInventories.Any())
                {
                    var completedCount = existingInventories.Count(x => x.Status == 99);
                    var inProgressCount = existingInventories.Count(x => x.Status != 99);

                    if (completedCount > 0 && inProgressCount == 0)
                    {
                        var message = $"开始盘点 - 当前月度盘点已完成 - 系统月度: {sysMonth}, 完成公司数: {completedCount}";
                        _logger.LogInformation("开始盘点 - 当前月度盘点已完成 - 系统月度: {SysMonth}, 完成公司数: {CompletedCount}", sysMonth, completedCount);
                        return (false, message, new List<InventoryItem>());
                    }
                    else if (inProgressCount > 0)
                    {
                        var message = $"开始盘点 - 当前月度盘点已存在 - 系统月度: {sysMonth}, 总公司数: {existingInventories.Count}, 进行中: {inProgressCount}, 已完成: {completedCount}";
                        _logger.LogInformation("开始盘点 - 当前月度盘点已存在 - 系统月度: {SysMonth}, 总公司数: {TotalCount}, 进行中: {InProgressCount}, 已完成: {CompletedCount}",
                            sysMonth, existingInventories.Count, inProgressCount, completedCount);
                        //返回未完成盘点的记录 
                        return (false, message, existingInventories.Where(x => x.Status != 99).ToList());
                    }
                }

                // 获取符合条件的公司列表（使用公共方法）
                var eligibleCompanies = await GetQualifiedCompaniesAsync(sysMonth, false);

                if (eligibleCompanies.Count == 0)
                {
                    var message = $"开始盘点 - 未找到符合条件的公司 - 系统月度: {sysMonth}";
                    _logger.LogWarning("开始盘点 - 未找到符合条件的公司 - 系统月度: {SysMonth}", sysMonth);
                    return (false, message, new List<InventoryItem>());
                }

                _logger.LogInformation("开始盘点 - 找到符合条件的公司 - 系统月度: {SysMonth}, 公司数量: {Count}", sysMonth, eligibleCompanies.Count);

                // 创建盘点记录
                var inventoryItems = new List<InventoryItem>();
                foreach (var company in eligibleCompanies)
                {
                    var inventoryItem = new InventoryItem
                    {
                        Id = Guid.NewGuid(),
                        CompanyId = Guid.Parse(company.Id),
                        CompanyName = company.Name,
                        CompanyLongName = company.Name, // 如果有完整名称字段可以使用
                        SysMonth = sysMonth,
                        Status = 2, // 待启动库存盘点
                        Store = "",
                        TempStore = "",
                        Operation = "",
                        Exchange = "",
                        ThirdStore = ""
                    };

                    inventoryItems.Add(inventoryItem);
                }

                // 批量插入到数据库
                var insertCount = await _inventoryRepository.AddMany(inventoryItems);
                if (insertCount > 0)
                {
                    _logger.LogInformation("开始盘点 - 创建子公司记录成功 - 系统月度: {SysMonth}, 创建数量: {Count}", sysMonth, inventoryItems.Count);
                    return (true, $"成功创建 {inventoryItems.Count} 个子公司盘点记录", inventoryItems);
                }
                else
                {
                    _logger.LogError("开始盘点 - 创建子公司记录失败 - 系统月度: {SysMonth}, 数据库插入返回0", sysMonth);
                    return (false, "创建盘点记录失败", new List<InventoryItem>());
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始盘点 - 创建子公司记录异常 - 系统月度: {SysMonth}, 错误: {Message}", sysMonth, ex.Message);
                return (false, $"创建失败: {ex.Message}", new List<InventoryItem>());
            }
        }


        /// <summary>
        /// 处理盘点记录生成事件（只处理外部能力中心业务）
        /// </summary>
        /// <param name="eventDto"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message)> ProcessInventoryGenerateEventAsync(InventoryGenerateEventDto eventDto)
        {
            try
            {
                _logger.LogInformation("盘点生成 - 接收外部盘点事件 - Topic: finance-inventory-generate, 系统月度: {SysMonth}, 公司数量: {Count}",
                    eventDto.SysMonth, eventDto.CompanyIds.Count);

                if (!eventDto.CompanyIds.Any())
                {
                    _logger.LogInformation("盘点生成 - 外部盘点事件无需处理 - 公司列表为空");
                    return (true, "没有需要处理的公司");
                }

                var successCount = 0;
                var failedCompanies = new List<Guid>();

                // 一次性获取所有公司的基础数据，避免N+1查询问题
                var companyIdStrings = eventDto.CompanyIds.Select(id => id.ToString()).ToList();
                var allCompanies = await _applyBFFService.GetCompanyInfosAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = companyIdStrings
                });

                // 创建公司信息字典，便于快速查找
                var companyInfoDict = allCompanies.ToDictionary(c => Guid.Parse(c.Id), c => c);
                _logger.LogInformation("盘点生成 - 获取公司基础信息成功 - 请求数量: {RequestCount}, 获取数量: {ActualCount}",
                    eventDto.CompanyIds.Count, allCompanies.Count);

                // 批量处理每个公司的外部能力中心盘点
                foreach (var companyId in eventDto.CompanyIds)
                {
                    try
                    {
                        if (companyInfoDict.TryGetValue(companyId, out var companyInfo))
                        {
                            // 只处理外部能力中心的盘点业务
                            var (success, message, triggeredActions) = await ProcessExternalInventoryAsync(companyId, eventDto.SysMonth, companyInfo.NameCode, companyInfo.Name);
                            if (success)
                            {
                                successCount++;
                                _logger.LogInformation("盘点生成 - 外部盘点处理成功 - 公司: {CompanyId}({CompanyName}), 触发动作数: {ActionCount}",
                                    companyId, companyInfo.Name, triggeredActions.Count);
                            }
                            else
                            {
                                failedCompanies.Add(companyId);
                                _logger.LogError("盘点生成 - 外部盘点处理失败 - 公司: {CompanyId}({CompanyName}), 错误: {Message}",
                                    companyId, companyInfo.Name, message);
                            }
                        }
                        else
                        {
                            failedCompanies.Add(companyId);
                            _logger.LogError("盘点生成 - 外部盘点处理失败 - 公司: {CompanyId}, 原因: 未找到公司信息", companyId);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCompanies.Add(companyId);
                        _logger.LogError(ex, "盘点生成 - 外部盘点处理异常 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                    }
                }

                var resultMessage = $"盘点生成 - 外部盘点处理完成 - 成功: {successCount}, 失败: {failedCompanies.Count}";
                _logger.LogInformation("盘点生成 - 外部盘点事件处理完成 - 系统月度: {SysMonth}, 成功: {SuccessCount}, 失败: {FailedCount}",
                    eventDto.SysMonth, successCount, failedCompanies.Count);

                return (failedCompanies.Count == 0, resultMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "盘点生成 - 外部盘点事件处理异常 - 系统月度: {SysMonth}, 错误: {Message}", eventDto.SysMonth, ex.Message);
                return (false, $"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量处理所有公司的财务内部盘点记录生成
        /// </summary>
        /// <param name="inventoryItems">已创建的盘点记录</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        public async Task<(bool Success, string Message)> ProcessFinanceInternalInventoryForAllCompaniesAsync(
            List<InventoryItem> inventoryItems, string sysMonth)
        {
            try
            {
                _logger.LogInformation("开始盘点 - 财务内部盘点批量处理 - 系统月度: {SysMonth}, 公司数量: {Count}", sysMonth, inventoryItems.Count);

                if (!inventoryItems.Any())
                {
                    _logger.LogInformation("开始盘点 - 财务内部盘点批量处理跳过 - 无需处理的盘点记录");
                    return (true, "没有需要处理的盘点记录");
                }

                var successCount = 0;
                var failedCompanies = new List<Guid>();

                // 一次性获取所有公司的基础数据，避免N+1查询问题
                var companyIds = inventoryItems.Select(item => item.CompanyId.ToString()).ToList();
                var allCompanies = await _applyBFFService.GetCompanyInfosAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = companyIds
                });

                // 创建公司信息字典，便于快速查找
                var companyInfoDict = allCompanies.ToDictionary(c => Guid.Parse(c.Id), c => c);
                _logger.LogInformation("开始盘点 - 获取公司基础信息成功 - 请求数量: {RequestCount}, 获取数量: {ActualCount}",
                    inventoryItems.Count, allCompanies.Count);

                // 批量处理每个公司的财务内部盘点
                foreach (var inventoryItem in inventoryItems)
                {
                    try
                    {
                        if (companyInfoDict.TryGetValue(inventoryItem.CompanyId, out var companyInfo))
                        {
                            var (success, message, generatedCodes) = await ProcessFinanceInternalInventoryAsync(
                                inventoryItem.CompanyId,
                                sysMonth,
                                companyInfo.NameCode,
                                companyInfo.Name);

                            if (success)
                            {
                                successCount++;
                                _logger.LogInformation("开始盘点 - 财务内部盘点处理成功 - 公司: {CompanyId}({CompanyName}), 生成盘点单数: {CodeCount}",
                                    inventoryItem.CompanyId, companyInfo.Name, generatedCodes.Count);
                            }
                            else
                            {
                                failedCompanies.Add(inventoryItem.CompanyId);
                                _logger.LogError("开始盘点 - 财务内部盘点处理失败 - 公司: {CompanyId}({CompanyName}), 错误: {Message}",
                                    inventoryItem.CompanyId, companyInfo.Name, message);
                            }
                        }
                        else
                        {
                            failedCompanies.Add(inventoryItem.CompanyId);
                            _logger.LogError("开始盘点 - 财务内部盘点处理失败 - 公司: {CompanyId}, 原因: 未找到公司信息", inventoryItem.CompanyId);
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCompanies.Add(inventoryItem.CompanyId);
                        _logger.LogError(ex, "开始盘点 - 财务内部盘点处理异常 - 公司: {CompanyId}, 错误: {Message}",
                            inventoryItem.CompanyId, ex.Message);
                    }
                }

                var resultMessage = $"开始盘点 - 财务内部盘点批量处理完成 - 成功: {successCount}, 失败: {failedCompanies.Count}";
                _logger.LogInformation("开始盘点 - 财务内部盘点批量处理完成 - 系统月度: {SysMonth}, 成功: {SuccessCount}, 失败: {FailedCount}",
                    sysMonth, successCount, failedCompanies.Count);

                return (failedCompanies.Count == 0, resultMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始盘点 - 财务内部盘点批量处理异常 - 系统月度: {SysMonth}, 错误: {Message}", sysMonth, ex.Message);
                return (false, $"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个公司的盘点生成
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message)> ProcessCompanyInventoryGenerationAsync(Guid companyId, string sysMonth)
        {
            try
            {
                _logger.LogInformation("开始处理公司 {CompanyId} 的盘点生成，月度: {SysMonth}", companyId, sysMonth);

                // 获取公司信息
                var companyList = await _applyBFFService.GetCompanyInfosAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { companyId.ToString() }
                });
                var companyInfo = companyList.FirstOrDefault();
                if (companyInfo == null)
                {
                    var companyMessage = $"未找到公司信息: {companyId}";
                    _logger.LogWarning(companyMessage);
                    return (false, companyMessage);
                }

                // 获取盘点单信息（简化权限处理，直接根据公司和月度获取）
                var inventory = await _inventoryMgmAppService.GetInventoryByCompanyAndMonth(companyId, sysMonth);

                if (inventory == null)
                {
                    var inventoryMessage = $"未找到公司 {companyId} 的盘点记录";
                    _logger.LogWarning(inventoryMessage);
                    return (false, inventoryMessage);
                }

                // 财务内部盘点处理（直接调用）
                var internalResult = await ProcessFinanceInternalInventoryAsync(companyId, sysMonth, companyInfo.NameCode, companyInfo.Name);

                // 外部能力中心盘点处理（事件驱动）
                var externalResult = await ProcessExternalInventoryAsync(companyId, sysMonth, companyInfo.NameCode, companyInfo.Name);

                var message = $"公司 {companyId} 盘点生成 完成 - 内部: {internalResult.Success}, 外部: {externalResult.Success}";
                _logger.LogInformation(message);

                return (internalResult.Success && externalResult.Success, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理公司 {CompanyId} 盘点生成 失败: {Message}", companyId, ex.Message);
                return (false, $"处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理财务内部盘点（直接调用模式）
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <param name="companyCode"></param>
        /// <param name="companyName"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message, Dictionary<string, string> GeneratedCodes)> ProcessFinanceInternalInventoryAsync(
            Guid companyId, string sysMonth, string companyCode, string companyName)
        {
            var fieldUpdates = new Dictionary<string, string>();
            var generatedCodes = new Dictionary<string, string>();
            var failedInventories = new List<string>();
            var processStartTime = DateTime.Now;

            try
            {
                _logger.LogInformation("开始处理财务内部盘点 - 公司: {CompanyId}({CompanyName}), 月度: {SysMonth}, 公司代码: {CompanyCode}, 处理时间: {ProcessTime}",
                    companyId, companyName, sysMonth, companyCode, processStartTime);

                // 获取盘点单信息（简化权限处理，直接根据公司和月度获取）
                var inventoryStartTime = DateTime.Now;
                var inventory = await _inventoryMgmAppService.GetInventoryByCompanyAndMonth(companyId, sysMonth);
                var inventoryDuration = DateTime.Now - inventoryStartTime;

                if (inventory == null)
                {
                    var errorMessage = $"未找到盘点记录 - 公司: {companyId}({companyName}), 月度: {sysMonth}，请检查该公司是否已创建盘点记录";
                    _logger.LogWarning("财务内部盘点失败 - 公司: {CompanyId}({CompanyName}), 月度: {SysMonth}, 原因: {ErrorMessage}, 查询耗时: {Duration}ms",
                        companyId, companyName, sysMonth, errorMessage, inventoryDuration.TotalMilliseconds);
                    return (false, errorMessage, generatedCodes);
                }

                _logger.LogInformation("获取盘点记录成功 - 公司: {CompanyId}({CompanyName}), 盘点ID: {InventoryId}, 状态: {Status}, 查询耗时: {Duration}ms",
                    companyId, companyName, inventory.Id, inventory.Status, inventoryDuration.TotalMilliseconds);

                // 应收盘点
                await ProcessCreditInventoryAsync(companyId, sysMonth, companyCode, companyName, inventory,
                    fieldUpdates, generatedCodes, failedInventories);

                // 已签收待开票盘点
                await ProcessReceivedNoInvoiceInventoryAsync(companyId, sysMonth, companyCode, companyName, inventory,
                    fieldUpdates, generatedCodes, failedInventories);

                // 应付盘点
                await ProcessDebtInventoryAsync(companyId, sysMonth, companyCode, companyName, inventory,
                    fieldUpdates, generatedCodes, failedInventories);

                // 付款盘点
                await ProcessPaymentInventoryAsync(companyId, sysMonth, companyCode, companyName, inventory,
                    fieldUpdates, generatedCodes, failedInventories);

                // 垫资盘点
                await ProcessAdvanceInventoryAsync(companyId, sysMonth, companyCode, companyName, inventory,
                    fieldUpdates, generatedCodes, failedInventories);

                // 批量更新字段
                if (fieldUpdates.Any())
                {
                    try
                    {
                        await _inventoryMgmAppService.UpdateInventoryItemFields(inventory.Id, fieldUpdates);
                        _logger.LogInformation("财务内部盘点字段批量更新成功 - 公司: {CompanyId}, 更新字段: {Fields}",
                            companyId, string.Join(", ", fieldUpdates.Keys));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "财务内部盘点字段批量更新失败 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                        return (false, $"字段更新失败: {ex.Message}", generatedCodes);
                    }
                }
                else
                {
                    _logger.LogWarning("财务内部盘点处理完成，但没有生成任何盘点单号 - 公司: {CompanyId}", companyId);
                }

                // 构建结果消息
                var successCount = generatedCodes.Count;
                var failedCount = failedInventories.Count;
                var totalProcessDuration = DateTime.Now - processStartTime;
                var message = $"财务内部盘点处理完成 - 成功: {successCount}, 失败: {failedCount}";

                if (failedInventories.Any())
                {
                    message += $", 失败项目: {string.Join(", ", failedInventories)}";
                    _logger.LogWarning("部分财务内部盘点处理失败 - 公司: {CompanyId}({CompanyName}), 月度: {SysMonth}, 失败项目: {FailedItems}, 总耗时: {Duration}ms",
                        companyId, companyName, sysMonth, string.Join(", ", failedInventories), totalProcessDuration.TotalMilliseconds);
                }

                _logger.LogInformation("财务内部盘点处理完成 - 公司: {CompanyId}({CompanyName}), 月度: {SysMonth}, 生成盘点单数: {Count}, 总耗时: {Duration}ms, 详情: {@Details}",
                    companyId, companyName, sysMonth, successCount, totalProcessDuration.TotalMilliseconds,
                    generatedCodes.Select(kv => new { Type = kv.Key, Code = kv.Value }).ToList());

                return (failedCount == 0, message, generatedCodes);
            }
            catch (Exception ex)
            {
                var totalProcessDuration = DateTime.Now - processStartTime;
                _logger.LogError(ex, "处理财务内部盘点异常 - 公司: {CompanyId}({CompanyName}), 月度: {SysMonth}, 错误类型: {ExceptionType}, 错误消息: {Message}, 堆栈跟踪: {StackTrace}, 总耗时: {Duration}ms",
                    companyId, companyName, sysMonth, ex.GetType().Name, ex.Message, ex.StackTrace?.Split('\n').Take(3).ToArray(), totalProcessDuration.TotalMilliseconds);
                return (false, $"处理失败: {ex.GetType().Name} - {ex.Message}", generatedCodes);
            }
        }

        /// <summary>
        /// 处理外部能力中心盘点（事件驱动模式）
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <param name="companyCode"></param>
        /// <param name="companyName"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message, List<InventoryActionType> TriggeredActions)> ProcessExternalInventoryAsync(
            Guid companyId, string sysMonth, string companyCode, string companyName)
        {
            var triggeredActions = new List<InventoryActionType>();

            try
            {
                _logger.LogInformation("开始处理外部能力中心盘点 - 公司: {CompanyId}", companyId);

                // 获取盘点单信息（简化权限处理，直接根据公司和月度获取）
                var inventory = await _inventoryMgmAppService.GetInventoryByCompanyAndMonth(companyId, sysMonth);

                if (inventory == null)
                {
                    return (false, "未找到盘点记录", triggeredActions);
                }

                // 构建需要执行的外部盘点动作列表
                var externalActions = new List<InventoryActionDto>();

                // 库存盘点
                if (string.IsNullOrEmpty(inventory.Store))
                {
                    externalActions.Add(new InventoryActionDto
                    {
                        ActionType = InventoryActionType.CreateStockInventory,
                        ActionName = "创建库存盘点",
                        NeedExecute = true,
                        Order = 1
                    });
                    triggeredActions.Add(InventoryActionType.CreateStockInventory);
                }

                // 暂存盘点
                if (string.IsNullOrEmpty(inventory.TempStore))
                {
                    externalActions.Add(new InventoryActionDto
                    {
                        ActionType = InventoryActionType.CreateTinyInventory,
                        ActionName = "创建暂存盘点",
                        NeedExecute = true,
                        Order = 2
                    });
                    triggeredActions.Add(InventoryActionType.CreateTinyInventory);
                }

                // 跟台盘点
                if (string.IsNullOrEmpty(inventory.Operation))
                {
                    externalActions.Add(new InventoryActionDto
                    {
                        ActionType = InventoryActionType.CreateSginyInventory,
                        ActionName = "创建跟台盘点",
                        NeedExecute = true,
                        Order = 3
                    });
                    triggeredActions.Add(InventoryActionType.CreateSginyInventory);
                }

                // 换货盘点
                if (string.IsNullOrEmpty(inventory.Exchange))
                {
                    externalActions.Add(new InventoryActionDto
                    {
                        ActionType = InventoryActionType.CreateExchangeInventory,
                        ActionName = "创建换货盘点",
                        NeedExecute = true,
                        Order = 4
                    });
                    triggeredActions.Add(InventoryActionType.CreateExchangeInventory);
                }

                // 待确认收入盘点
                if (string.IsNullOrEmpty(inventory.SureIncomeCode))
                {
                    externalActions.Add(new InventoryActionDto
                    {
                        ActionType = InventoryActionType.CreateSureIncomeInventory,
                        ActionName = "创建待确认收入盘点",
                        NeedExecute = true,
                        Order = 5
                    });
                    triggeredActions.Add(InventoryActionType.CreateSureIncomeInventory);
                }

                // 如果有外部盘点需要处理，发布事件
                if (externalActions.Any())
                {
                    var createOtherCheckEvent = new CreateOtherCheckEventDto
                    {
                        CompanyId = companyId,
                        InventoryItemId = inventory.Id,
                        Status = inventory.Status,
                        SysMonth = sysMonth,
                        UserId = Guid.Empty, // 系统自动
                        UserName = "系统自动",
                        CompanyCode = companyCode,
                        CompanyName = companyName,
                        Actions = externalActions
                    };

                    // 发布到现有的盘点创建事件处理器
                    await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, "finance-inventory-createothercheck", createOtherCheckEvent);
                    _logger.LogInformation("发布外部盘点事件成功 - 公司: {CompanyId}, 动作数量: {Count}", companyId, externalActions.Count);
                }

                var message = $"外部能力中心盘点处理完成 - 触发 {triggeredActions.Count} 个盘点动作";
                return (true, message, triggeredActions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理外部能力中心盘点失败 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                return (false, $"处理失败: {ex.Message}", triggeredActions);
            }
        }

        /// <summary>
        /// 处理盘点完成检查事件
        /// 优化后的流程：直接从盘点表获取数据 → 检查盘点完成情况 → 更新系统月度 → 更新盘点状态 → 发布完成通知事件
        /// </summary>
        /// <param name="eventDto">盘点完成检查事件数据</param>
        /// <returns>处理结果、消息和已完成的公司ID列表</returns>
        public async Task<(bool Success, string Message, List<Guid> CompletedCompanyIds)> ProcessCompleteInventoryCheckEventAsync(CompleteInventoryCheckEventDto eventDto)
        {
            var startTime = DateTime.Now;

            try
            {
                _logger.LogInformation("结束盘点 - 开始处理完成检查事件 - 系统月度: {SysMonth}, 触发时间: {TriggerTime}, 备注: {Remark}",
                    eventDto.SysMonth, eventDto.TriggerTime, eventDto.Remark);

                // 步骤1：直接从盘点表获取指定系统月度的盘点记录（状态不为99已完成的）
                var step1StartTime = DateTime.Now;
                var allInventoryRecords = await _inventoryRepository.GetInventoriesBySysMonth(eventDto.SysMonth);
                var step1Duration = DateTime.Now - step1StartTime;

                // 过滤掉已完成的盘点记录
                var pendingInventoryRecords = allInventoryRecords.Where(x => x.Status != 99).ToList();

                _logger.LogInformation("结束盘点 - 步骤1完成：获取盘点记录 - 系统月度: {SysMonth}, 总盘点记录数: {TotalCount}, 待处理记录数: {PendingCount}, 耗时: {Duration}ms",
                    eventDto.SysMonth, allInventoryRecords.Count, pendingInventoryRecords.Count, step1Duration.TotalMilliseconds);

                if (!pendingInventoryRecords.Any())
                {
                    var message = $"[ERROR_CODE:NO_PENDING_INVENTORY_RECORDS] 未找到待处理的盘点记录 - 系统月度: {eventDto.SysMonth}，所有盘点记录可能已完成";
                    _logger.LogWarning("[ERROR_CODE:NO_PENDING_INVENTORY_RECORDS] 结束盘点 - 处理终止：{Message}", message);
                    return (false, message, new List<Guid>());
                }

                // 步骤2：逐个检查盘点完成情况
                var step2StartTime = DateTime.Now;
                var qualifiedCompanies = new List<CompanyInventoryInfo>();
                var failedCompanies = new List<(Guid CompanyId, string CompanyName, string Reason)>();

                foreach (var inventory in pendingInventoryRecords)
                {
                    var (isCompleted, message, _) = await CheckInventoryCompletionAsync(inventory.CompanyId, eventDto.SysMonth);
                    if (isCompleted)
                    {
                        qualifiedCompanies.Add(new CompanyInventoryInfo
                        {
                            CompanyId = inventory.CompanyId,
                            CompanyName = inventory.CompanyName ?? inventory.CompanyId.ToString(),
                            InventoryId = inventory.Id
                        });
                    }
                    else
                    {
                        failedCompanies.Add((inventory.CompanyId, inventory.CompanyName ?? inventory.CompanyId.ToString(), message));
                    }
                }

                var step2Duration = DateTime.Now - step2StartTime;

                _logger.LogInformation("结束盘点 - 步骤2完成：检查盘点完成情况 - 系统月度: {SysMonth}, 检查公司数: {CheckCount}, 通过数: {PassedCount}, 失败数: {FailedCount}, 耗时: {Duration}ms",
                    eventDto.SysMonth, pendingInventoryRecords.Count, qualifiedCompanies.Count, failedCompanies.Count, step2Duration.TotalMilliseconds);

                if (failedCompanies.Any())
                {
                    _logger.LogWarning("结束盘点 - 步骤2失败详情 - 系统月度: {SysMonth}, 失败公司: {@FailedCompanies}",
                        eventDto.SysMonth, failedCompanies.Select(f => new { CompanyId = f.CompanyId, CompanyName = f.CompanyName, Reason = f.Reason }).ToList());
                }

                if (qualifiedCompanies.Count == 0)
                {
                    var message = $"[ERROR_CODE:NO_COMPANIES_PASSED_CHECK] 无公司通过盘点完成检查 - 系统月度: {eventDto.SysMonth}, 检查公司数: {pendingInventoryRecords.Count}, 失败数: {failedCompanies.Count}，请检查各公司的盘点进度和返利计提状态";
                    _logger.LogWarning("[ERROR_CODE:NO_COMPANIES_PASSED_CHECK] 结束盘点 - 处理终止：{Message}", message);
                    return (false, message, new List<Guid>());
                }

                // 步骤3：对检查通过的公司，直接更新盘点状态为99并更新系统月度
                var step3StartTime = DateTime.Now;
                var completedCompanyIds = new List<Guid>();
                var failedUpdates = new List<(Guid CompanyId, string CompanyName, string Reason)>();

                foreach (var company in qualifiedCompanies)
                {
                    try
                    {
                        // 更新盘点状态为已完成（99）
                        await _inventoryMgmAppService.UpdateInventoryStatus(company.InventoryId, 99);

                        // 更新系统月度
                        var newSystemPeriod = DateTime.Parse(eventDto.SysMonth + "-01").AddMonths(1).ToString("yyyy-MM");
                        await _bDSApiClient.UpdateSysMonth(new List<Guid> { company.CompanyId }, newSystemPeriod);

                        completedCompanyIds.Add(company.CompanyId);

                        _logger.LogInformation("结束盘点 - 公司处理成功 - 公司: {CompanyId}({CompanyName}), 系统月度: {SysMonth}",
                            company.CompanyId, company.CompanyName, eventDto.SysMonth);
                    }
                    catch (Exception ex)
                    {
                        var errorMessage = $"更新失败: {ex.Message}";
                        failedUpdates.Add((company.CompanyId, company.CompanyName, errorMessage));

                        _logger.LogError(ex, "[ERROR_CODE:COMPANY_UPDATE_FAILED] 结束盘点 - 公司处理失败 - 公司: {CompanyId}({CompanyName}), 系统月度: {SysMonth}, 错误: {Error}",
                            company.CompanyId, company.CompanyName, eventDto.SysMonth, ex.Message);
                    }
                }

                var step3Duration = DateTime.Now - step3StartTime;

                _logger.LogInformation("结束盘点 - 步骤3完成：更新盘点状态和系统月度 - 系统月度: {SysMonth}, 成功数: {SuccessCount}, 失败数: {FailedCount}, 耗时: {Duration}ms",
                    eventDto.SysMonth, completedCompanyIds.Count, failedUpdates.Count, step3Duration.TotalMilliseconds);



                // 步骤4：发布完成通知事件（对成功的公司）
                if (completedCompanyIds.Any())
                {
                    var step4StartTime = DateTime.Now;
                    var newSystemPeriod = DateTime.Parse(eventDto.SysMonth + "-01").AddMonths(1).ToString("yyyy-MM");

                    try
                    {
                        await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, "finance-inventory-finishinventory", new
                        {
                            SysMonth = eventDto.SysMonth,
                            NewSystemPeriod = newSystemPeriod,
                            SystemPeriodUpdated = true,
                            CompanyIds = completedCompanyIds,
                            TriggerTime = DateTime.Now,
                            Remark = "盘点完成自动通知"
                        });

                        var step4Duration = DateTime.Now - step4StartTime;
                        _logger.LogInformation("结束盘点 - 步骤4完成：发布完成通知事件 - 系统月度: {SysMonth}, 成功公司数: {Count}, 耗时: {Duration}ms",
                            eventDto.SysMonth, completedCompanyIds.Count, step4Duration.TotalMilliseconds);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[ERROR_CODE:NOTIFICATION_FAILED] 结束盘点 - 发布完成通知事件失败 - 系统月度: {SysMonth}, 错误: {Error}",
                            eventDto.SysMonth, ex.Message);
                    }
                }

                // 构建最终结果
                var totalDuration = DateTime.Now - startTime;
                var successMessage = completedCompanyIds.Any()
                    ? $"盘点完成处理成功，共完成 {completedCompanyIds.Count} 家公司"
                    : "无公司完成盘点处理";

                if (failedUpdates.Any())
                {
                    successMessage += $"，失败 {failedUpdates.Count} 家公司";
                }

                _logger.LogInformation("结束盘点 - 处理完成 - 系统月度: {SysMonth}, 最终成功公司数: {FinalSuccessCount}, 失败公司数: {FailedCount}, 总耗时: {TotalDuration}ms",
                    eventDto.SysMonth, completedCompanyIds.Count, failedUpdates.Count, totalDuration.TotalMilliseconds);

                return (failedUpdates.Count == 0, successMessage, completedCompanyIds);
            }
            catch (Exception ex)
            {
                var totalDuration = DateTime.Now - startTime;
                _logger.LogError(ex, "结束盘点 - 完成检查事件处理异常 - 系统月度: {SysMonth}, 错误类型: {ExceptionType}, 错误消息: {Message}, 堆栈跟踪: {StackTrace}, 总耗时: {TotalDuration}ms, 触发时间: {TriggerTime}, 备注: {Remark}",
                    eventDto.SysMonth, ex.GetType().Name, ex.Message, ex.StackTrace?.Split('\n').Take(3).ToArray(), totalDuration.TotalMilliseconds, eventDto.TriggerTime, eventDto.Remark);
                return (false, $"处理失败: {ex.GetType().Name} - {ex.Message}", new List<Guid>());
            }
        }

        /// <summary>
        /// 检查盘点完成情况
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<(bool IsCompleted, string Message, List<string> MissingFields)> CheckInventoryCompletionAsync(Guid companyId, string sysMonth)
        {
            var missingFields = new List<string>();

            try
            {
                _logger.LogInformation("开始检查盘点完成情况 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);

                // 获取盘点单信息
                var inventory = await _inventoryMgmAppService.GetInventoryByCompanyAndMonth(companyId, sysMonth);
                if (inventory == null)
                {
                    var message = $"[ERROR_CODE:INVENTORY_RECORD_NOT_FOUND] 未找到盘点记录 - 公司ID: {companyId}, 系统月度: {sysMonth}";
                    _logger.LogWarning("[ERROR_CODE:INVENTORY_RECORD_NOT_FOUND] 盘点完成检查失败 - 公司: {CompanyId}, 月度: {SysMonth}, 原因: 未找到盘点记录", companyId, sysMonth);
                    return (false, message, missingFields);
                }

                // 检查盘点单状态，如果已经完成则直接返回成功
                if (inventory.Status == 99) // 99表示已完成
                {
                    _logger.LogInformation("盘点已完成跳过检查 - 公司: {CompanyId}, 月度: {SysMonth}, 盘点单ID: {InventoryId}", companyId, sysMonth, inventory.Id);
                    return (true, "盘点已完成", missingFields);
                }

                // 依次检查 InventoryActionType 枚举中对应的盘点单号是否不为空
                // 参考 FinishInventory 方法的判断逻辑
                var inventoryFields = new Dictionary<InventoryActionType, (string FieldName, string? FieldValue)>
                {
                    { InventoryActionType.CreateTinyInventory, ("TempStore", inventory.TempStore) },
                    { InventoryActionType.CreateSginyInventory, ("Operation", inventory.Operation) },
                    { InventoryActionType.CreateExchangeInventory, ("Exchange", inventory.Exchange) },
                    { InventoryActionType.CreateSureIncomeInventory, ("SureIncomeCode", inventory.SureIncomeCode) },
                    { InventoryActionType.CreateCreditRecordInventory, ("CreditRecordCode", inventory.CreditRecordCode) },
                    { InventoryActionType.CreateReceivedNoInvoiceInventory, ("ReceivedNoInvoiceRecordCode", inventory.ReceivedNoInvoiceRecordCode) },
                    { InventoryActionType.CreateDebtRecordInventory, ("DebtRecordCode", inventory.DebtRecordCode) },
                    { InventoryActionType.CreatePaymentRecordInventory, ("PaymentRecordCode", inventory.PaymentRecordCode) },
                    { InventoryActionType.CreateAdvanceRecordInventory, ("AdvanceRecordCode", inventory.AdvanceRecordCode) }
                };

                // 检查必需的盘点单号是否都已生成
                foreach (var field in inventoryFields)
                {
                    if (string.IsNullOrEmpty(field.Value.FieldValue))
                    {
                        missingFields.Add(field.Value.FieldName);
                    }
                    else if (field.Value.FieldValue == "生成中")
                    {
                        var message = $"[ERROR_CODE:INVENTORY_GENERATING] 业务单据正在生成中，无法设定完成 - 字段: {field.Value.FieldName}";
                        _logger.LogWarning("[ERROR_CODE:INVENTORY_GENERATING] 盘点完成检查失败 - 公司: {CompanyId}, 月度: {SysMonth}, 原因: {Reason}", companyId, sysMonth, message);
                        return (false, message, missingFields);
                    }
                }

                if (missingFields.Any())
                {
                    var message = $"[ERROR_CODE:INVENTORY_CODES_MISSING] 盘点单号未完成 - 缺失字段: {string.Join(", ", missingFields)}";
                    _logger.LogWarning("[ERROR_CODE:INVENTORY_CODES_MISSING] 盘点完成检查失败 - 公司: {CompanyId}, 月度: {SysMonth}, 缺失字段: {MissingFields}", companyId, sysMonth, string.Join(", ", missingFields));
                    return (false, message, missingFields);
                }

                // 检查返利计提状态
                var rebateCheckResult = await CheckRebateProvisionStatusAsync(companyId, sysMonth);
                if (!rebateCheckResult.IsCompleted)
                {
                    var message = $"[ERROR_CODE:REBATE_PROVISION_INCOMPLETE] 返利计提未完成 - {rebateCheckResult.Message}";
                    _logger.LogWarning("[ERROR_CODE:REBATE_PROVISION_INCOMPLETE] 盘点完成检查失败 - 公司: {CompanyId}, 月度: {SysMonth}, 原因: 返利计提未完成", companyId, sysMonth);
                    return (false, message, missingFields);
                }

                _logger.LogInformation("盘点完成检查通过 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);
                return (true, "盘点完成检查通过", missingFields);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ERROR_CODE:INVENTORY_CHECK_EXCEPTION] 检查盘点完成情况异常 - 公司: {CompanyId}, 月度: {SysMonth}, 错误: {Message}", companyId, sysMonth, ex.Message);
                return (false, $"[ERROR_CODE:INVENTORY_CHECK_EXCEPTION] 检查失败: {ex.Message}", missingFields);
            }
        }

        /// <summary>
        /// 为所有公司处理返利计提状态并推送到金蝶（开始盘点时调用）
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        private async Task ProcessRebateProvisionForAllCompaniesAsync(List<Guid> companyIds, string sysMonth)
        {
            try
            {
                _logger.LogInformation("开始盘点 - 返利计提批量处理 - 系统月度: {SysMonth}, 公司数量: {Count}", sysMonth, companyIds.Count);

                foreach (var companyId in companyIds)
                {
                    try
                    {
                        await ProcessRebateProvisionForCompanyAsync(companyId, sysMonth);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "开始盘点 - 返利计提处理失败 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                        // 单个公司失败不影响其他公司的处理
                    }
                }

                _logger.LogInformation("开始盘点 - 返利计提批量处理完成 - 系统月度: {SysMonth}, 公司数量: {Count}", sysMonth, companyIds.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始盘点 - 返利计提批量处理异常 - 系统月度: {SysMonth}, 错误: {Message}", sysMonth, ex.Message);
            }
        }

        /// <summary>
        /// 为单个公司处理返利计提状态并推送到金蝶
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        private async Task ProcessRebateProvisionForCompanyAsync(Guid companyId, string sysMonth)
        {
            try
            {
                _logger.LogInformation("开始盘点 - 返利计提处理 - 公司: {CompanyId}, 系统月度: {SysMonth}", companyId, sysMonth);

                // 解析系统月度，构建查询时间范围
                var sysDate = DateTime.Parse(sysMonth + "-01");
                var startDate = sysDate;
                var endDate = sysDate.AddMonths(1).AddDays(-1);

                // 直接从数据库查询返利计提记录，不通过QueryService（避免权限策略）
                var rebateList = await _rebateProvisionItemRepository.GetByCompanyIdAndDateRangeAsync(companyId, startDate, endDate);

                if (!rebateList.Any())
                {
                    _logger.LogInformation("开始盘点 - 返利计提跳过 - 公司: {CompanyId}, 系统月度: {SysMonth}, 原因: 无返利计提记录", companyId, sysMonth);
                    return;
                }

                // 处理未完成的返利计提记录
                var incompleteRebates = rebateList.Where(r => r.Status != StatusEnum.Complate).ToList();
                if (incompleteRebates.Any())
                {
                    _logger.LogInformation("开始盘点 - 返利计提提交金蝶 - 公司: {CompanyId}, 系统月度: {SysMonth}, 未完成记录数: {Count}",
                        companyId, sysMonth, incompleteRebates.Count);

                    foreach (var rebate in incompleteRebates)
                    {
                        try
                        {
                            _logger.LogInformation("开始盘点 - 返利计提提交金蝶 - 单据号: {BillCode}, 状态: {Status}", rebate.BillCode, rebate.Status);

                            // 提交返利计提到金蝶
                            if (rebate.Id != Guid.Empty)
                            {
                                var submitResult = await _rebateProvisionQueryService.Submit(rebate.Id);

                                if (submitResult.Code == CodeStatusEnum.Success)
                                {
                                    _logger.LogInformation("开始盘点 - 返利计提提交金蝶成功 - 单据号: {BillCode}", rebate.BillCode);
                                }
                                else
                                {
                                    _logger.LogWarning("开始盘点 - 返利计提提交金蝶失败 - 单据号: {BillCode}, 错误: {ErrorMessage}",
                                        rebate.BillCode, submitResult.Message);
                                }
                            }
                            else
                            {
                                _logger.LogWarning("开始盘点 - 返利计提跳过 - 单据号: {BillCode}, 原因: ID为空", rebate.BillCode);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "开始盘点 - 返利计提提交金蝶异常 - 单据号: {BillCode}, 错误: {Message}",
                                rebate.BillCode, ex.Message);
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("开始盘点 - 返利计提已完成 - 公司: {CompanyId}, 系统月度: {SysMonth}, 记录数: {Count}",
                        companyId, sysMonth, rebateList.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始盘点 - 返利计提处理异常 - 公司: {CompanyId}, 系统月度: {SysMonth}, 错误: {Message}", companyId, sysMonth, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 检查返利计提状态（直接从数据库查询，不调用包含权限策略的通用方法）
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="sysMonth">系统月度（格式：yyyy-MM）</param>
        /// <returns>返回检查结果和详细信息</returns>
        private async Task<(bool IsCompleted, string Message)> CheckRebateProvisionStatusAsync(Guid companyId, string sysMonth)
        {
            try
            {
                _logger.LogInformation("开始检查返利计提状态 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);

                // 解析系统月度，构建查询时间范围
                var sysDate = DateTime.Parse(sysMonth + "-01");
                var startDate = sysDate;
                var endDate = sysDate.AddMonths(1).AddDays(-1);

                // 直接从数据库查询返利计提记录，不通过QueryService（避免权限策略）
                var rebateList = await _rebateProvisionItemRepository.GetByCompanyIdAndDateRangeAsync(companyId, startDate, endDate);

                if (!rebateList.Any())
                {
                    // 如果没有返利计提记录，认为通过检查（可能该公司该月度无需返利计提）
                    _logger.LogInformation("公司 {CompanyId} 在 {SysMonth} 无返利计提记录，检查通过", companyId, sysMonth);
                    return (true, "无返利计提记录，检查通过");
                }

                // 检查所有返利计提记录是否都已完成
                var incompleteRebates = rebateList.Where(r => r.Status != StatusEnum.Complate).ToList();

                if (incompleteRebates.Any())
                {
                    // 构建详细的未完成返利计提信息
                    var incompleteDetails = incompleteRebates.Select(r => $"单据号:{r.BillCode},状态:{r.Status}").ToList();
                    var message = $"有 {incompleteRebates.Count} 个未完成的返利计提记录: {string.Join("; ", incompleteDetails)}";
                    _logger.LogWarning("公司 {CompanyId} 在 {SysMonth} 返利计提未完成 - 详情: {Details}",
                        companyId, sysMonth, string.Join("; ", incompleteDetails));
                    return (false, message);
                }

                _logger.LogInformation("公司 {CompanyId} 在 {SysMonth} 的所有 {Count} 个返利计提记录都已完成",
                    companyId, sysMonth, rebateList.Count);
                return (true, $"所有 {rebateList.Count} 个返利计提记录都已完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查返利计提状态失败 - 公司: {CompanyId}, 月度: {SysMonth}, 错误: {Message}", companyId, sysMonth, ex.Message);
                return (false, $"检查失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取符合条件的公司列表（公共方法）
        /// </summary>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="requireActiveStatus">是否要求公司状态为激活状态（Status == 1）</param>
        /// <returns>符合条件的公司列表</returns>
        private async Task<List<CompanyInfoOutput>> GetQualifiedCompaniesAsync(string sysMonth, bool requireActiveStatus = false)
        {
            try
            {
                _logger.LogInformation("获取符合条件的公司列表 - 系统月度: {SysMonth}, 要求激活状态: {RequireActiveStatus}",
                    sysMonth, requireActiveStatus);

                // 获取所有公司信息
                var companyList = await _applyBFFService.GetCompanyInfosAsync(new Application.CompetenceCenter.BDSCenter.BaseEnableInput());

                // 应用过滤条件
                var eligibleCompanies = companyList.Where(c => c.SysMonth == sysMonth);

                if (requireActiveStatus)
                {
                    eligibleCompanies = eligibleCompanies.Where(c => c.Status == 1);
                }

                var result = eligibleCompanies.ToList();

                _logger.LogInformation("找到 {Count} 个符合条件的公司 - 系统月度: {SysMonth}, 要求激活状态: {RequireActiveStatus}",
                    result.Count, sysMonth, requireActiveStatus);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取符合条件的公司列表时发生异常 - 系统月度: {SysMonth}", sysMonth);
                return new List<CompanyInfoOutput>();
            }
        }

        /// <summary>
        /// 创建盘点记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message, List<Guid> CreatedRecordIds)> CreateInventoryRecordsAsync(CreateInventoryRecordRequestDto request)
        {
            var createdRecordIds = new List<Guid>();

            try
            {
                _logger.LogInformation("开始创建 盘点记录 - 盘点单ID: {InventoryItemId}, 动作数量: {Count}",
                    request.InventoryItemId, request.ActionTypes.Count);

                var records = new List<InventoryRecord>();

                foreach (var actionType in request.ActionTypes)
                {
                    var record = new InventoryRecord
                    {
                        Id = Guid.NewGuid(),
                        InventoryItemId = request.InventoryItemId,
                        CompanyId = request.CompanyId,
                        SysMonth = request.SysMonth,
                        ActionType = actionType,
                        ActionName = GetActionName(actionType),
                        Status = (int)InventoryRecordStatus.Pending,
                        RetryCount = 0
                    };

                    records.Add(record);
                    createdRecordIds.Add(record.Id);
                }

                // 注意：这里需要实际的仓储实现来保存记录
                // await _inventoryRecordRepository.AddManyAsync(records);

                var message = $"成功创建 {createdRecordIds.Count} 个盘点记录";
                _logger.LogInformation(message);
                return (true, message, createdRecordIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建盘点记录失败: {Message}", ex.Message);
                return (false, $"创建失败: {ex.Message}", createdRecordIds);
            }
        }

        /// <summary>
        /// 批量处理盘点记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<(bool Success, string Message, int ProcessedCount)> BatchProcessInventoryRecordsAsync(BatchProcessInventoryRecordRequestDto request)
        {
            try
            {
                _logger.LogInformation("开始批量处理 盘点记录 - 记录数量: {Count}, 目标状态: {Status}",
                    request.RecordIds.Count, request.TargetStatus);

                // 注意：这里需要实际的仓储实现来批量更新状态
                // var processedCount = await _inventoryRecordRepository.BatchUpdateStatusAsync(
                //     request.RecordIds, request.TargetStatus, request.ErrorMessage);
                var processedCount = request.RecordIds.Count; // 临时返回

                var message = $"成功处理 {processedCount} 个盘点记录";
                _logger.LogInformation(message);
                return (true, message, processedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量处理 盘点记录 失败: {Message}", ex.Message);
                return (false, $"处理失败: {ex.Message}", 0);
            }
        }

        /// <summary>
        /// 获取盘点进度
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="sysMonth"></param>
        /// <returns></returns>
        public async Task<InventoryProgressResponseDto> GetInventoryProgressAsync(Guid companyId, string sysMonth)
        {
            try
            {
                _logger.LogInformation("获取盘点进度 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);

                // 获取公司信息
                var companyList = await _applyBFFService.GetCompanyInfosAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { companyId.ToString() }
                });
                var companyInfo = companyList.FirstOrDefault();

                // 获取盘点单信息（简化权限处理，直接根据公司和月度获取）
                var inventory = await _inventoryMgmAppService.GetInventoryByCompanyAndMonth(companyId, sysMonth);

                var response = new InventoryProgressResponseDto
                {
                    CompanyId = companyId,
                    CompanyName = companyInfo?.Name ?? "未知公司",
                    SysMonth = sysMonth,
                    InventoryItemId = inventory?.Id ?? Guid.Empty
                };

                if (inventory != null)
                {

                    // 构建盘点动作进度列表
                    var actionProgress = new List<InventoryActionProgressDto>
                    {
                        new() { ActionType = InventoryActionType.CreateStockInventory, ActionName = "库存盘点", InventoryCode = inventory.Store },
                        new() { ActionType = InventoryActionType.CreateTinyInventory, ActionName = "暂存盘点", InventoryCode = inventory.TempStore },
                        new() { ActionType = InventoryActionType.CreateSginyInventory, ActionName = "跟台盘点", InventoryCode = inventory.Operation },
                        new() { ActionType = InventoryActionType.CreateExchangeInventory, ActionName = "换货盘点", InventoryCode = inventory.Exchange },
                        new() { ActionType = InventoryActionType.CreateSureIncomeInventory, ActionName = "待确认收入盘点", InventoryCode = inventory.SureIncomeCode },
                        new() { ActionType = InventoryActionType.CreateCreditRecordInventory, ActionName = "应收盘点", InventoryCode = inventory.CreditRecordCode },
                        new() { ActionType = InventoryActionType.CreateReceivedNoInvoiceInventory, ActionName = "已签收待开票盘点", InventoryCode = inventory.ReceivedNoInvoiceRecordCode },
                        new() { ActionType = InventoryActionType.CreateDebtRecordInventory, ActionName = "应付盘点", InventoryCode = inventory.DebtRecordCode },
                        new() { ActionType = InventoryActionType.CreatePaymentRecordInventory, ActionName = "付款盘点", InventoryCode = inventory.PaymentRecordCode },
                        new() { ActionType = InventoryActionType.CreateAdvanceRecordInventory, ActionName = "垫资盘点", InventoryCode = inventory.AdvanceRecordCode }
                    };

                    // 设置状态
                    foreach (var action in actionProgress)
                    {
                        action.Status = string.IsNullOrEmpty(action.InventoryCode) ? InventoryRecordStatus.Pending : InventoryRecordStatus.Completed;
                        action.UpdatedTime = DateTime.UtcNow;
                    }

                    response.ActionProgress = actionProgress;
                    response.TotalActionTypes = actionProgress.Count;
                    response.CompletedActionTypes = actionProgress.Count(a => a.Status == InventoryRecordStatus.Completed);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取盘点进度失败 - 公司: {CompanyId}, 月度: {SysMonth}", companyId, sysMonth);
                return new InventoryProgressResponseDto
                {
                    CompanyId = companyId,
                    CompanyName = "获取失败",
                    SysMonth = sysMonth
                };
            }
        }



        /// <summary>
        /// 处理应收盘点
        /// </summary>
        private async Task ProcessCreditInventoryAsync(Guid companyId, string sysMonth, string companyCode, string companyName,
            InventoryDTO inventory, Dictionary<string, string> fieldUpdates, Dictionary<string, string> generatedCodes,
            List<string> failedInventories)
        {
            if (!string.IsNullOrEmpty(inventory.CreditRecordCode))
            {
                _logger.LogInformation("应收盘点已存在 - 公司: {CompanyId}, 单号: {Code}", companyId, inventory.CreditRecordCode);
                generatedCodes["应收盘点"] = inventory.CreditRecordCode;
                return;
            }

            try
            {
                _logger.LogInformation("开始处理应收盘点 - 公司: {CompanyId}", companyId);

                var (executeCount, creditCode) = await _inventoryMgmAppService.CreditRecordInventory(
                    companyId, sysMonth, companyCode, companyName, "系统自动");

                if (executeCount > 0 && !string.IsNullOrEmpty(creditCode))
                {
                    fieldUpdates["CreditRecordCode"] = creditCode;
                    generatedCodes["应收盘点"] = creditCode;
                    _logger.LogInformation("应收盘点创建成功 - 公司: {CompanyId}, 盘点单号: {Code}, 影响行数: {ExecuteCount}",
                        companyId, creditCode, executeCount);
                }
                else if (executeCount == -1)
                {
                    // 无符合条件的数据，但仍需要写入单号以标记已处理
                    fieldUpdates["CreditRecordCode"] = creditCode;
                    generatedCodes["应收盘点"] = creditCode;
                    _logger.LogInformation("应收盘点跳过 - 公司: {CompanyId}, 原因: 无符合条件的应收数据, 已写入单号: {Code}", companyId, creditCode);
                }
                else
                {
                    var reason = executeCount == 0 ? "数据插入失败" : "单号生成失败";
                    _logger.LogError("应收盘点创建失败 - 公司: {CompanyId}, 原因: {Reason}, 执行行数: {ExecuteCount}, 单号: {Code}",
                        companyId, reason, executeCount, creditCode ?? "空");
                    failedInventories.Add($"应收盘点({reason})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应收盘点处理异常 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                failedInventories.Add($"应收盘点(异常: {ex.Message})");
            }
        }

        /// <summary>
        /// 处理已签收待开票盘点（应收盘点）
        /// </summary>
        private async Task ProcessReceivedNoInvoiceInventoryAsync(Guid companyId, string sysMonth, string companyCode, string companyName,
            InventoryDTO inventory, Dictionary<string, string> fieldUpdates, Dictionary<string, string> generatedCodes,
            List<string> failedInventories)
        {
            if (!string.IsNullOrEmpty(inventory.ReceivedNoInvoiceRecordCode))
            {
                _logger.LogInformation("已签收待开票盘点已存在 - 公司: {CompanyId}, 单号: {Code}", companyId, inventory.ReceivedNoInvoiceRecordCode);
                generatedCodes["已签收待开票盘点"] = inventory.ReceivedNoInvoiceRecordCode;
                return;
            }

            try
            {
                _logger.LogInformation("开始处理已签收待开票盘点 - 公司: {CompanyId}", companyId);

                var (executeCount, receivedCode) = await _inventoryMgmAppService.ReceivedNoInvoiceInventory(
                    companyId, sysMonth, companyCode, companyName, "系统自动");

                if (executeCount > 0 && !string.IsNullOrEmpty(receivedCode))
                {
                    fieldUpdates["ReceivedNoInvoiceRecordCode"] = receivedCode;
                    generatedCodes["已签收待开票盘点"] = receivedCode;
                    _logger.LogInformation("已签收待开票盘点创建成功 - 公司: {CompanyId}, 盘点单号: {Code}, 影响行数: {ExecuteCount}",
                        companyId, receivedCode, executeCount);
                }
                else if (executeCount == -1)
                {
                    // 无符合条件的数据，但仍需要写入单号以标记已处理
                    fieldUpdates["ReceivedNoInvoiceRecordCode"] = receivedCode;
                    generatedCodes["已签收待开票盘点"] = receivedCode;
                    _logger.LogInformation("已签收待开票盘点跳过 - 公司: {CompanyId}, 原因: 无符合条件的数据, 已写入单号: {Code}", companyId, receivedCode);
                }
                else
                {
                    var reason = executeCount == 0 ? "数据插入失败" : "单号生成失败";
                    _logger.LogError("已签收待开票盘点创建失败 - 公司: {CompanyId}, 原因: {Reason}, 执行行数: {ExecuteCount}, 单号: {Code}",
                        companyId, reason, executeCount, receivedCode ?? "空");
                    failedInventories.Add($"已签收待开票盘点({reason})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "已签收待开票盘点处理异常 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                failedInventories.Add($"已签收待开票盘点(异常: {ex.Message})");
            }
        }

        /// <summary>
        /// 处理应付盘点
        /// </summary>
        private async Task ProcessDebtInventoryAsync(Guid companyId, string sysMonth, string companyCode, string companyName,
            InventoryDTO inventory, Dictionary<string, string> fieldUpdates, Dictionary<string, string> generatedCodes,
            List<string> failedInventories)
        {
            if (!string.IsNullOrEmpty(inventory.DebtRecordCode))
            {
                _logger.LogInformation("应付盘点已存在 - 公司: {CompanyId}, 单号: {Code}", companyId, inventory.DebtRecordCode);
                generatedCodes["应付盘点"] = inventory.DebtRecordCode;
                return;
            }

            try
            {
                _logger.LogInformation("开始处理应付盘点 - 公司: {CompanyId}", companyId);

                var (executeCount, debtCode) = await _inventoryMgmAppService.DebtRecordInventory(
                    companyId, sysMonth, companyCode, companyName, "系统自动");

                if (executeCount > 0 && !string.IsNullOrEmpty(debtCode))
                {
                    fieldUpdates["DebtRecordCode"] = debtCode;
                    generatedCodes["应付盘点"] = debtCode;
                    _logger.LogInformation("应付盘点创建成功 - 公司: {CompanyId}, 盘点单号: {Code}, 影响行数: {ExecuteCount}",
                        companyId, debtCode, executeCount);
                }
                else if (executeCount == -1)
                {
                    // 无符合条件的数据，但仍需要写入单号以标记已处理
                    fieldUpdates["DebtRecordCode"] = debtCode;
                    generatedCodes["应付盘点"] = debtCode;
                    _logger.LogInformation("应付盘点跳过 - 公司: {CompanyId}, 原因: 无符合条件的应付数据, 已写入单号: {Code}", companyId, debtCode);
                }
                else
                {
                    var reason = executeCount == 0 ? "数据插入失败" : "单号生成失败";
                    _logger.LogError("应付盘点创建失败 - 公司: {CompanyId}, 原因: {Reason}, 执行行数: {ExecuteCount}, 单号: {Code}",
                        companyId, reason, executeCount, debtCode ?? "空");
                    failedInventories.Add($"应付盘点({reason})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应付盘点处理异常 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                failedInventories.Add($"应付盘点(异常: {ex.Message})");
            }
        }

        /// <summary>
        /// 处理付款盘点
        /// </summary>
        private async Task ProcessPaymentInventoryAsync(Guid companyId, string sysMonth, string companyCode, string companyName,
            InventoryDTO inventory, Dictionary<string, string> fieldUpdates, Dictionary<string, string> generatedCodes,
            List<string> failedInventories)
        {
            if (!string.IsNullOrEmpty(inventory.PaymentRecordCode))
            {
                _logger.LogInformation("付款盘点已存在 - 公司: {CompanyId}, 单号: {Code}", companyId, inventory.PaymentRecordCode);
                generatedCodes["付款盘点"] = inventory.PaymentRecordCode;
                return;
            }

            try
            {
                _logger.LogInformation("开始处理付款盘点 - 公司: {CompanyId}", companyId);

                var (executeCount, paymentCode) = await _inventoryMgmAppService.PaymentRecordInventory(
                    companyId, sysMonth, companyCode, companyName, "系统自动");

                if (executeCount > 0 && !string.IsNullOrEmpty(paymentCode))
                {
                    fieldUpdates["PaymentRecordCode"] = paymentCode;
                    generatedCodes["付款盘点"] = paymentCode;
                    _logger.LogInformation("付款盘点创建成功 - 公司: {CompanyId}, 盘点单号: {Code}, 影响行数: {ExecuteCount}",
                        companyId, paymentCode, executeCount);
                }
                else if (executeCount == -1)
                {
                    // 无符合条件的数据，但仍需要写入单号以标记已处理
                    fieldUpdates["PaymentRecordCode"] = paymentCode;
                    generatedCodes["付款盘点"] = paymentCode;
                    _logger.LogInformation("付款盘点跳过 - 公司: {CompanyId}, 原因: 无符合条件的付款数据, 已写入单号: {Code}", companyId, paymentCode);
                }
                else
                {
                    var reason = executeCount == 0 ? "数据插入失败" : "单号生成失败";
                    _logger.LogError("付款盘点创建失败 - 公司: {CompanyId}, 原因: {Reason}, 执行行数: {ExecuteCount}, 单号: {Code}",
                        companyId, reason, executeCount, paymentCode ?? "空");
                    failedInventories.Add($"付款盘点({reason})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "付款盘点处理异常 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                failedInventories.Add($"付款盘点(异常: {ex.Message})");
            }
        }

        /// <summary>
        /// 处理垫资盘点
        /// </summary>
        private async Task ProcessAdvanceInventoryAsync(Guid companyId, string sysMonth, string companyCode, string companyName,
            InventoryDTO inventory, Dictionary<string, string> fieldUpdates, Dictionary<string, string> generatedCodes,
            List<string> failedInventories)
        {
            if (!string.IsNullOrEmpty(inventory.AdvanceRecordCode))
            {
                _logger.LogInformation("垫资盘点已存在 - 公司: {CompanyId}, 单号: {Code}", companyId, inventory.AdvanceRecordCode);
                generatedCodes["垫资盘点"] = inventory.AdvanceRecordCode;
                return;
            }

            try
            {
                _logger.LogInformation("开始处理垫资盘点 - 公司: {CompanyId}", companyId);

                var (executeCount, advanceCode) = await _inventoryMgmAppService.AdvanceRecordInventory(
                    companyId, sysMonth, companyCode, companyName, "系统自动");

                if (executeCount > 0 && !string.IsNullOrEmpty(advanceCode))
                {
                    fieldUpdates["AdvanceRecordCode"] = advanceCode;
                    generatedCodes["垫资盘点"] = advanceCode;
                    _logger.LogInformation("垫资盘点创建成功 - 公司: {CompanyId}, 盘点单号: {Code}, 影响行数: {ExecuteCount}",
                        companyId, advanceCode, executeCount);
                }
                else if (executeCount == -1)
                {
                    // 无符合条件的数据，但仍需要写入单号以标记已处理
                    fieldUpdates["AdvanceRecordCode"] = advanceCode;
                    generatedCodes["垫资盘点"] = advanceCode;
                    _logger.LogInformation("垫资盘点跳过 - 公司: {CompanyId}, 原因: 无符合条件的垫资数据, 已写入单号: {Code}", companyId, advanceCode);
                }
                else
                {
                    var reason = executeCount == 0 ? "数据插入失败" : "单号生成失败";
                    _logger.LogError("垫资盘点创建失败 - 公司: {CompanyId}, 原因: {Reason}, 执行行数: {ExecuteCount}, 单号: {Code}",
                        companyId, reason, executeCount, advanceCode ?? "空");
                    failedInventories.Add($"垫资盘点({reason})");
                }
            }
            catch (Exception ex)
            {
                // 垫资盘点依赖外部集成平台，可能因为网络或数据问题失败
                _logger.LogError(ex, "垫资盘点处理异常 - 公司: {CompanyId}, 错误: {Message}", companyId, ex.Message);
                failedInventories.Add($"垫资盘点(异常: {ex.Message})");
            }
        }

        #region 盘点完成检查辅助方法

        /// <summary>
        /// 检查所有公司的盘点完成情况（批量优化版本）
        /// </summary>
        /// <param name="targetCompanies">目标公司列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns>检查结果</returns>
        private async Task<InventoryCompletionCheckResult> CheckInventoryCompletionForAllCompaniesAsync(
            List<CompanyInfoOutput> targetCompanies, string sysMonth)
        {
            var qualifiedCompanies = new List<CompanyInventoryInfo>();
            var failedCompanies = new List<(Guid CompanyId, string CompanyName, string Reason)>();

            _logger.LogInformation("结束盘点 - 开始批量检查盘点完成情况 - 系统月度: {SysMonth}, 目标公司数: {Count}",
                sysMonth, targetCompanies.Count);

            try
            {
                // 步骤1：批量获取所有公司的盘点记录
                var companyIds = targetCompanies.Select(c => Guid.Parse(c.Id)).ToList();
                var inventoryDict = await GetInventoriesByCompaniesAndMonthBatchAsync(companyIds, sysMonth);

                _logger.LogInformation("结束盘点 - 批量获取盘点记录完成 - 系统月度: {SysMonth}, 请求公司数: {RequestCount}, 获取记录数: {RecordCount}",
                    sysMonth, companyIds.Count, inventoryDict.Count);

                // 步骤2：批量检查盘点完成情况
                var completionResults = await CheckInventoryCompletionBatchAsync(companyIds, sysMonth);

                _logger.LogInformation("结束盘点 - 批量完成检查完成 - 系统月度: {SysMonth}, 检查公司数: {CheckCount}, 通过数: {PassedCount}",
                    sysMonth, companyIds.Count, completionResults.Count(r => r.Value.IsCompleted));

                // 步骤3：组合结果
                foreach (var company in targetCompanies)
                {
                    var companyId = Guid.Parse(company.Id);

                    try
                    {
                        // 获取盘点记录
                        if (inventoryDict.TryGetValue(companyId, out var inventory))
                        {
                            // 过滤已经是完成状态(status=99)的盘点单，避免重复处理
                            if (inventory.Status == 99)
                            {
                                _logger.LogInformation("结束盘点 - 跳过已完成盘点 - 公司: {CompanyId}({CompanyName}), 盘点单ID: {InventoryId}, 状态: {Status}",
                                    companyId, company.Name, inventory.Id, inventory.Status);
                                continue; // 跳过已完成的盘点单
                            }

                            // 检查完成情况
                            if (completionResults.TryGetValue(companyId, out var completionResult) && completionResult.IsCompleted)
                            {
                                qualifiedCompanies.Add(new CompanyInventoryInfo
                                {
                                    CompanyId = companyId,
                                    CompanyName = company.Name,
                                    InventoryId = inventory.Id,
                                    CurrentStatus = inventory.Status,
                                    NeedsStatusUpdate = inventory.Status != 99
                                });

                                _logger.LogInformation("结束盘点 - 完成检查通过 - 公司: {CompanyId}({CompanyName}), 盘点单ID: {InventoryId}, 当前状态: {Status}",
                                    companyId, company.Name, inventory.Id, inventory.Status);
                            }
                            else
                            {
                                var reason = completionResults.TryGetValue(companyId, out var result) ? result.Message : "检查失败";
                                failedCompanies.Add((companyId, company.Name, reason));
                                _logger.LogWarning("结束盘点 - 完成检查未通过 - 公司: {CompanyId}({CompanyName}), 原因: {Reason}",
                                    companyId, company.Name, reason);
                            }
                        }
                        else
                        {
                            failedCompanies.Add((companyId, company.Name, "未找到盘点记录"));
                            _logger.LogWarning("结束盘点 - 未找到盘点记录 - 公司: {CompanyId}({CompanyName})", companyId, company.Name);
                        }

                    }
                    catch (Exception ex)
                    {
                        failedCompanies.Add((companyId, company.Name, $"检查异常: {ex.Message}"));
                        _logger.LogError(ex, "结束盘点 - 检查盘点完成情况异常 - 公司: {CompanyId}({CompanyName}), 错误: {Message}",
                            companyId, company.Name, ex.Message);
                    }
                }

                _logger.LogInformation("结束盘点 - 批量盘点完成检查结果 - 系统月度: {SysMonth}, 通过: {PassedCount}, 失败: {FailedCount}",
                    sysMonth, qualifiedCompanies.Count, failedCompanies.Count);

                return new InventoryCompletionCheckResult
                {
                    QualifiedCompanies = qualifiedCompanies,
                    FailedCompanies = failedCompanies
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ERROR_CODE:BATCH_CHECK_EXCEPTION] 结束盘点 - 批量检查盘点完成情况异常 - 系统月度: {SysMonth}, 错误: {Message}", sysMonth, ex.Message);

                // 异常情况下，所有公司都标记为失败
                var allFailedCompanies = targetCompanies.Select(c => (Guid.Parse(c.Id), c.Name, $"[ERROR_CODE:BATCH_CHECK_EXCEPTION] 批量检查异常: {ex.Message}")).ToList();
                return new InventoryCompletionCheckResult
                {
                    QualifiedCompanies = new List<CompanyInventoryInfo>(),
                    FailedCompanies = allFailedCompanies
                };
            }
        }

        /// <summary>
        /// 为公司列表更新系统月度（批量优化版本）
        /// </summary>
        /// <param name="qualifiedCompanies">符合条件的公司列表</param>
        /// <param name="currentSysMonth">当前系统月度</param>
        /// <returns>更新结果</returns>
        private async Task<SystemMonthUpdateResult> UpdateSystemMonthForCompaniesAsync(
            List<CompanyInventoryInfo> qualifiedCompanies, string currentSysMonth)
        {
            var newSystemPeriod = DateTime.Parse(currentSysMonth + "-01").AddMonths(1).ToString("yyyy-MM");

            _logger.LogInformation("结束盘点 - 开始批量更新系统月度 - 当前月度: {CurrentMonth}, 目标月度: {NewSystemPeriod}, 公司数量: {Count}",
                currentSysMonth, newSystemPeriod, qualifiedCompanies.Count);

            // 使用批量更新方法
            var (successfulCompanies, failedCompanies) = await UpdateSystemMonthBatchAsync(qualifiedCompanies, newSystemPeriod);

            _logger.LogInformation("结束盘点 - 批量系统月度更新完成 - 总数: {Total}, 成功: {Success}, 失败: {Failed}",
                qualifiedCompanies.Count, successfulCompanies.Count, failedCompanies.Count);

            return new SystemMonthUpdateResult
            {
                SuccessfulCompanies = successfulCompanies,
                FailedCompanies = failedCompanies,
                NewSystemPeriod = newSystemPeriod
            };
        }

        /// <summary>
        /// 为公司列表更新盘点状态（批量优化版本）
        /// </summary>
        /// <param name="successfulSysMonthCompanies">系统月度更新成功的公司列表</param>
        /// <param name="allQualifiedCompanies">所有符合条件的公司列表</param>
        /// <returns>更新结果</returns>
        private async Task<InventoryStatusUpdateResult> UpdateInventoryStatusForCompaniesAsync(
            List<CompanyInventoryInfo> successfulSysMonthCompanies, List<CompanyInventoryInfo> allQualifiedCompanies)
        {
            var successfulCompanies = new List<CompanyInventoryInfo>();
            var failedCompanies = new List<(Guid CompanyId, string CompanyName, string ErrorMessage)>();

            _logger.LogInformation("结束盘点 - 开始批量更新盘点状态 - 需要更新的公司数量: {Count}",
                successfulSysMonthCompanies.Count);

            try
            {
                // 筛选出需要状态更新的公司
                var companiesNeedingUpdate = successfulSysMonthCompanies.Where(c => c.NeedsStatusUpdate).ToList();
                var companiesAlreadyCompleted = successfulSysMonthCompanies.Where(c => !c.NeedsStatusUpdate).ToList();

                _logger.LogInformation("结束盘点 - 盘点状态更新分析 - 需要更新: {NeedUpdate}, 已完成: {AlreadyCompleted}",
                    companiesNeedingUpdate.Count, companiesAlreadyCompleted.Count);

                // 批量更新需要更新状态的盘点单
                if (companiesNeedingUpdate.Any())
                {
                    var updateResults = await UpdateInventoryStatusBatchAsync(companiesNeedingUpdate);
                    successfulCompanies.AddRange(updateResults.SuccessfulCompanies);
                    failedCompanies.AddRange(updateResults.FailedCompanies);
                }

                // 已完成的公司直接加入成功列表
                foreach (var company in companiesAlreadyCompleted)
                {
                    successfulCompanies.Add(company);
                    _logger.LogInformation("结束盘点 - 盘点状态已完成跳过 - 公司: {CompanyId}({CompanyName}), 盘点单ID: {InventoryId}",
                        company.CompanyId, company.CompanyName, company.InventoryId);
                }

                _logger.LogInformation("结束盘点 - 批量盘点状态更新完成 - 总数: {Total}, 成功: {Success}, 失败: {Failed}",
                    successfulSysMonthCompanies.Count, successfulCompanies.Count, failedCompanies.Count);

                return new InventoryStatusUpdateResult
                {
                    SuccessfulCompanies = successfulCompanies,
                    FailedCompanies = failedCompanies
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "结束盘点 - 批量盘点状态更新异常 - 错误: {Message}", ex.Message);

                // 异常情况下，所有公司都标记为失败
                var allFailedCompanies = successfulSysMonthCompanies.Select(c => (c.CompanyId, c.CompanyName, $"批量更新异常: {ex.Message}")).ToList();
                return new InventoryStatusUpdateResult
                {
                    SuccessfulCompanies = new List<CompanyInventoryInfo>(),
                    FailedCompanies = allFailedCompanies
                };
            }
        }

        /// <summary>
        /// 批量更新盘点状态
        /// </summary>
        /// <param name="companies">需要更新的公司列表</param>
        /// <returns>更新结果</returns>
        private async Task<(List<CompanyInventoryInfo> SuccessfulCompanies, List<(Guid CompanyId, string CompanyName, string ErrorMessage)> FailedCompanies)>
            UpdateInventoryStatusBatchAsync(List<CompanyInventoryInfo> companies)
        {
            _logger.LogInformation("结束盘点 - 批量更新盘点状态 - 公司数: {Count}", companies.Count);

            try
            {
                // 使用真正的批量更新，一次数据库调用更新所有记录
                var inventoryIds = companies.Select(c => c.InventoryId).ToList();
                var affectedRows = await _inventoryMgmAppService.BatchUpdateInventoryStatus(inventoryIds, 99, "盘点完成检查自动更新");

                _logger.LogInformation("结束盘点 - 批量盘点状态更新完成 - 总数: {Total}, 更新成功: {Success}",
                    companies.Count, affectedRows);

                // 如果更新成功的数量与预期一致，则所有公司都成功
                if (affectedRows == companies.Count)
                {
                    foreach (var company in companies)
                    {
                        _logger.LogInformation("结束盘点 - 盘点状态更新成功 - 公司: {CompanyId}({CompanyName}), 盘点单ID: {InventoryId}",
                            company.CompanyId, company.CompanyName, company.InventoryId);
                    }
                    return (companies, new List<(Guid CompanyId, string CompanyName, string ErrorMessage)>());
                }
                else
                {
                    // 部分更新失败，需要逐个检查（这种情况比较少见）
                    _logger.LogWarning("结束盘点 - 批量更新部分失败 - 预期: {Expected}, 实际: {Actual}", companies.Count, affectedRows);

                    var successfulCompanies = new List<CompanyInventoryInfo>();
                    var failedCompanies = new List<(Guid CompanyId, string CompanyName, string ErrorMessage)>();

                    // 这里可以添加逐个验证的逻辑，或者简单地按比例分配成功/失败
                    // 为简化起见，假设前affectedRows个成功，其余失败
                    for (int i = 0; i < companies.Count; i++)
                    {
                        if (i < affectedRows)
                        {
                            successfulCompanies.Add(companies[i]);
                        }
                        else
                        {
                            failedCompanies.Add((companies[i].CompanyId, companies[i].CompanyName, "批量更新部分失败"));
                        }
                    }

                    return (successfulCompanies, failedCompanies);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "结束盘点 - 批量盘点状态更新异常 - 公司数: {Count}, 错误: {Message}", companies.Count, ex.Message);

                // 全部失败
                var allFailedCompanies = companies.Select(c => (c.CompanyId, c.CompanyName, $"批量更新异常: {ex.Message}")).ToList();
                return (new List<CompanyInventoryInfo>(), allFailedCompanies);
            }
        }

        /// <summary>
        /// 发布盘点完成通知事件
        /// </summary>
        /// <param name="successfulCompanies">成功完成的公司列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <param name="newSystemPeriod">新系统月度</param>
        /// <returns>发布结果</returns>
        private async Task<NotificationPublishResult> PublishInventoryFinishNotificationAsync(
            List<CompanyInventoryInfo> successfulCompanies, string sysMonth, string newSystemPeriod)
        {
            try
            {
                if (successfulCompanies.Count == 0)
                {
                    _logger.LogInformation("结束盘点 - 无需发布完成通知事件 - 无成功完成的公司");
                    return new NotificationPublishResult
                    {
                        Success = true,
                        Message = "无需发布事件",
                        NotifiedCompanyIds = []
                    };
                }

                var companyIds = successfulCompanies.Select(c => c.CompanyId).ToList();
                var finishEvent = new InventoryFinishEventDto
                {
                    CompanyIds = companyIds,
                    SysMonth = sysMonth,
                    SystemPeriodUpdated = true,
                    NewSystemPeriod = newSystemPeriod
                };

                _logger.LogInformation("结束盘点 - 发布完成通知事件 - Topic: finance-inventory-finishinventory, 公司数量: {Count}",
                    companyIds.Count);
                await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, "finance-inventory-finishinventory", finishEvent);
                _logger.LogInformation("结束盘点 - 发布完成通知事件成功 - Topic: finance-inventory-finishinventory, 公司数量: {Count}",
                    companyIds.Count);

                return new NotificationPublishResult
                {
                    Success = true,
                    Message = $"成功发布完成通知事件，通知公司数: {companyIds.Count}",
                    NotifiedCompanyIds = companyIds
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "结束盘点 - 发布完成通知事件失败 - 系统月度: {SysMonth}, 错误: {Message}",
                    sysMonth, ex.Message);
                return new NotificationPublishResult
                {
                    Success = false,
                    Message = $"发布事件失败: {ex.Message}",
                    NotifiedCompanyIds = []
                };
            }
        }

        /// <summary>
        /// 批量获取公司盘点记录
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns>公司ID到盘点记录的字典</returns>
        private async Task<Dictionary<Guid, InventoryDTO>> GetInventoriesByCompaniesAndMonthBatchAsync(List<Guid> companyIds, string sysMonth)
        {
            _logger.LogInformation("结束盘点 - 批量获取盘点记录 - 系统月度: {SysMonth}, 公司数: {Count}", sysMonth, companyIds.Count);

            // 使用真正的批量查询，一次数据库调用获取所有记录
            var inventories = await _inventoryMgmAppService.GetInventoriesByCompaniesAndMonth(companyIds, sysMonth);
            var inventoryDict = inventories.ToDictionary(inv => inv.CompanyId, inv => inv);

            _logger.LogInformation("结束盘点 - 批量获取盘点记录完成 - 系统月度: {SysMonth}, 请求: {RequestCount}, 获取: {ResultCount}",
                sysMonth, companyIds.Count, inventoryDict.Count);

            return inventoryDict;
        }

        /// <summary>
        /// 批量检查盘点完成情况（修复并发DbContext问题）
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns>公司ID到检查结果的字典</returns>
        private async Task<Dictionary<Guid, (bool IsCompleted, string Message)>> CheckInventoryCompletionBatchAsync(List<Guid> companyIds, string sysMonth)
        {
            _logger.LogInformation("结束盘点 - 批量检查盘点完成情况 - 系统月度: {SysMonth}, 公司数: {Count}", sysMonth, companyIds.Count);

            var results = new Dictionary<Guid, (bool IsCompleted, string Message)>();

            // 修复并发DbContext问题：改为顺序执行，避免同一个DbContext实例被多个线程并发访问
            foreach (var companyId in companyIds)
            {
                try
                {
                    var (isCompleted, message, _) = await CheckInventoryCompletionAsync(companyId, sysMonth);
                    results[companyId] = (isCompleted, message);

                    _logger.LogDebug("结束盘点 - 单个公司检查完成 - 公司: {CompanyId}, 结果: {IsCompleted}, 消息: {Message}",
                        companyId, isCompleted, message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[ERROR_CODE:COMPANY_CHECK_EXCEPTION] 结束盘点 - 检查公司 {CompanyId} 盘点完成情况失败: {Message}", companyId, ex.Message);
                    results[companyId] = (false, $"[ERROR_CODE:COMPANY_CHECK_EXCEPTION] 检查失败: {ex.Message}");
                }
            }

            var passedCount = results.Count(r => r.Value.IsCompleted);
            _logger.LogInformation("结束盘点 - 批量检查盘点完成情况完成 - 系统月度: {SysMonth}, 检查: {CheckCount}, 通过: {PassedCount}",
                sysMonth, results.Count, passedCount);

            return results;
        }

        /// <summary>
        /// 批量更新系统月度（简化版本：只使用批量接口）
        /// </summary>
        /// <param name="companyInfos">公司信息列表</param>
        /// <param name="newSystemPeriod">新系统月度</param>
        /// <returns>更新结果</returns>
        private async Task<(List<CompanyInventoryInfo> SuccessfulCompanies, List<(Guid CompanyId, string CompanyName, string ErrorMessage)> FailedCompanies)>
            UpdateSystemMonthBatchAsync(List<CompanyInventoryInfo> companyInfos, string newSystemPeriod)
        {
            _logger.LogInformation("结束盘点 - 批量更新系统月度 - 目标月度: {NewSystemPeriod}, 公司数: {Count}",
                newSystemPeriod, companyInfos.Count);

            var successfulCompanies = new List<CompanyInventoryInfo>();
            var failedCompanies = new List<(Guid CompanyId, string CompanyName, string ErrorMessage)>();

            try
            {
                // 使用批量更新接口
                var companyIds = companyInfos.Select(c => c.CompanyId).ToList();
                var batchUpdateResult = await _bDSApiClient.UpdateSysMonth(companyIds, newSystemPeriod);

                if (batchUpdateResult)
                {
                    // 批量更新成功，所有公司都成功
                    successfulCompanies.AddRange(companyInfos);
                    _logger.LogInformation("结束盘点 - 批量系统月度更新成功 - 目标月度: {NewSystemPeriod}, 成功公司数: {Count}, 公司列表: {@CompanyList}",
                        newSystemPeriod, companyInfos.Count, companyInfos.Select(c => new { Id = c.CompanyId, Name = c.CompanyName }).ToList());
                }
                else
                {
                    // 批量更新失败，所有公司都失败
                    foreach (var company in companyInfos)
                    {
                        failedCompanies.Add((company.CompanyId, company.CompanyName, "批量更新系统月度失败"));
                    }
                    _logger.LogError("结束盘点 - 批量系统月度更新失败 - 目标月度: {NewSystemPeriod}, 失败公司数: {Count}",
                        newSystemPeriod, companyInfos.Count);
                }
            }
            catch (Exception ex)
            {
                // 批量接口调用异常，所有公司都失败
                foreach (var company in companyInfos)
                {
                    failedCompanies.Add((company.CompanyId, company.CompanyName, $"批量更新系统月度异常: {ex.Message}"));
                }
                _logger.LogError(ex, "结束盘点 - 批量系统月度更新异常 - 目标月度: {NewSystemPeriod}, 公司数: {Count}, 错误: {ErrorMessage}",
                    newSystemPeriod, companyInfos.Count, ex.Message);
            }

            _logger.LogInformation("结束盘点 - 批量系统月度更新完成 - 总数: {Total}, 成功: {Success}, 失败: {Failed}",
                companyInfos.Count, successfulCompanies.Count, failedCompanies.Count);

            return (successfulCompanies, failedCompanies);
        }

        /// <summary>
        /// 构建最终结果
        /// </summary>
        /// <param name="checkResult">检查结果</param>
        /// <param name="sysMonthResult">系统月度更新结果</param>
        /// <param name="statusUpdateResult">状态更新结果</param>
        /// <param name="notificationResult">通知发布结果</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns>最终结果</returns>
        private (bool Success, string Message, List<Guid> CompletedCompanyIds) BuildFinalResult(
            InventoryCompletionCheckResult checkResult,
            SystemMonthUpdateResult sysMonthResult,
            InventoryStatusUpdateResult statusUpdateResult,
            NotificationPublishResult notificationResult,
            string sysMonth)
        {
            var completedCompanyIds = statusUpdateResult.SuccessfulCompanies.Select(c => c.CompanyId).ToList();

            // 统计各阶段结果
            var totalChecked = checkResult.QualifiedCompanies.Count + checkResult.FailedCompanies.Count;
            var checkPassed = checkResult.QualifiedCompanies.Count;
            var sysMonthUpdated = sysMonthResult.SuccessfulCompanies.Count;
            var statusUpdated = statusUpdateResult.SuccessfulCompanies.Count;
            var notified = notificationResult.NotifiedCompanyIds.Count;

            _logger.LogInformation("结束盘点 - 完成检查事件处理汇总 - 系统月度: {SysMonth}, " +
                "总检查: {TotalChecked}, 检查通过: {CheckPassed}, 系统月度更新: {SysMonthUpdated}, " +
                "状态更新: {StatusUpdated}, 通知发布: {Notified}",
                sysMonth, totalChecked, checkPassed, sysMonthUpdated, statusUpdated, notified);

            // 记录失败详情
            if (checkResult.FailedCompanies.Any())
            {
                var failedCheckDetails = string.Join("; ", checkResult.FailedCompanies.Select(f => $"{f.CompanyName}({f.CompanyId}): {f.Reason}"));
                _logger.LogWarning("结束盘点 - 盘点检查失败详情 - {FailedDetails}", failedCheckDetails);
            }

            if (sysMonthResult.FailedCompanies.Any())
            {
                var failedSysMonthDetails = string.Join("; ", sysMonthResult.FailedCompanies.Select(f => $"{f.CompanyName}({f.CompanyId}): {f.ErrorMessage}"));
                _logger.LogWarning("结束盘点 - 系统月度更新失败详情 - {FailedDetails}", failedSysMonthDetails);
            }

            if (statusUpdateResult.FailedCompanies.Any())
            {
                var failedStatusDetails = string.Join("; ", statusUpdateResult.FailedCompanies.Select(f => $"{f.CompanyName}({f.CompanyId}): {f.ErrorMessage}"));
                _logger.LogWarning("结束盘点 - 盘点状态更新失败详情 - {FailedDetails}", failedStatusDetails);
            }

            // 判断整体成功状态
            var overallSuccess = completedCompanyIds.Any() && notificationResult.Success;
            var message = $"盘点完成检查处理完成 - 系统月度: {sysMonth}, 最终完成公司数: {completedCompanyIds.Count}";

            if (overallSuccess)
            {
                _logger.LogInformation("结束盘点 - 完成检查事件处理成功 - {Message}", message);
            }
            else
            {
                _logger.LogWarning("结束盘点 - 完成检查事件处理部分失败 - {Message}", message);
            }

            return (overallSuccess, message, completedCompanyIds);
        }

        /// <summary>
        /// 根据动作类型获取动作名称
        /// </summary>
        /// <param name="actionType"></param>
        /// <returns></returns>
        private static string GetActionName(InventoryActionType actionType)
        {
            return actionType switch
            {
                InventoryActionType.CreateStockInventory => "创建库存盘点",
                InventoryActionType.CreateTinyInventory => "创建暂存盘点",
                InventoryActionType.CreateSginyInventory => "创建跟台盘点",
                InventoryActionType.CreateExchangeInventory => "创建换货盘点",
                InventoryActionType.CreateSureIncomeInventory => "创建待确认收入盘点",
                InventoryActionType.CreateCreditRecordInventory => "创建应收盘点",
                InventoryActionType.CreateReceivedNoInvoiceInventory => "创建已签收待开票盘点",
                InventoryActionType.CreateDebtRecordInventory => "创建应付盘点",
                InventoryActionType.CreatePaymentRecordInventory => "创建付款盘点",
                InventoryActionType.CreateAdvanceRecordInventory => "创建垫资盘点",
                InventoryActionType.UpdateActualInventoryCompleted => "更新实盘完成状态",
                _ => "未知动作"
            };
        }

        #endregion

        #endregion
    }
}
