using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord
{
    /// <summary>
    /// 盘点事件基础模型
    /// </summary>
    public class InventoryEventBaseDto
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 操作用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 操作用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 事件ID，用于幂等性控制
        /// </summary>
        public Guid EventId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTimeOffset EventTime { get; set; } = DateTimeOffset.UtcNow;
    }

    /// <summary>
    /// 创建其他盘点事件（统一事件）
    /// </summary>
    public class CreateOtherCheckEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 盘点单状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 需要执行的盘点动作列表
        /// </summary>
        public List<InventoryActionDto> Actions { get; set; } = new List<InventoryActionDto>();
    }

    /// <summary>
    /// 盘点动作定义
    /// </summary>
    public class InventoryActionDto
    {
        /// <summary>
        /// 动作类型
        /// </summary>
        public InventoryActionType ActionType { get; set; }

        /// <summary>
        /// 动作名称
        /// </summary>
        public string ActionName { get; set; }

        /// <summary>
        /// 是否需要执行（基于当前盘点单状态判断）
        /// </summary>
        public bool NeedExecute { get; set; }

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 动作参数（JSON格式）
        /// </summary>
        public string? Parameters { get; set; }
    }



    /// <summary>
    /// 开启盘点事件DTO
    /// </summary>
    public class StartInventoryEventDto
    {
        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string? SysMonth { get {return InventoryMonth; } }

        /// <summary>
        /// 触发时间
        /// </summary>
        public DateTime? TriggerTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 系统月度
        /// </summary>
        public string? InventoryMonth { get; set; }
    }

    /// <summary>
    /// 盘点记录生成事件DTO
    /// </summary>
    public class InventoryGenerateEventDto
    {
        /// <summary>
        /// 公司ID列表
        /// </summary>
        public List<Guid> CompanyIds { get; set; } = new List<Guid>();

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string SysMonth { get; set; }
    }

    /// <summary>
    /// 盘点完成检查事件DTO
    /// </summary>
    public class CompleteInventoryCheckEventDto
    {
        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime? TriggerTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 盘点完成广播事件DTO
    /// </summary>
    public class InventoryFinishEventDto
    {
        /// <summary>
        /// 公司ID列表
        /// </summary>
        public List<Guid> CompanyIds { get; set; } = new List<Guid>();

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 系统月度是否已更新
        /// </summary>
        public bool SystemPeriodUpdated { get; set; }

        /// <summary>
        /// 新的系统月度
        /// </summary>
        public string? NewSystemPeriod { get; set; }
    }

    /// <summary>
    /// 更新盘点单号请求DTO
    /// </summary>
    public class UpdateInventoryItemFieldsRequestDto
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 盘点单号更新列表
        /// </summary>
        public List<InventoryFieldUpdateDto> FieldUpdates { get; set; } = new List<InventoryFieldUpdateDto>();
    }

    /// <summary>
    /// 盘点字段更新DTO
    /// </summary>
    public class InventoryFieldUpdateDto
    {
        /// <summary>
        /// 盘点动作类型
        /// </summary>
        public InventoryActionType ActionType { get; set; }

        /// <summary>
        /// 盘点单号
        /// </summary>
        public string? InventoryCode { get; set; }
        /// <summary>
        /// 是否实盘
        /// </summary>
        public bool? IsActualInventoryCompleted { get; set; } = false;
        /// <summary>
        /// 动作名称（可选，用于日志记录）
        /// </summary>
        public string? ActionName { get; set; }
    }

    /// <summary>
    /// 更新盘点单号响应DTO
    /// </summary>
    public class UpdateInventoryItemFieldsResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 更新的字段数量
        /// </summary>
        public int UpdatedFieldCount { get; set; }

        /// <summary>
        /// 失败的字段列表
        /// </summary>
        public List<string> FailedFields { get; set; } = new List<string>();

        /// <summary>
        /// 库存盘点特殊处理结果（当包含库存盘点时）
        /// </summary>
        public List<StoreInventorySpecialFormatDto>? StoreInventoryResults { get; set; }
    }

    /// <summary>
    /// 库存盘点特殊格式DTO
    /// </summary>
    public class StoreInventorySpecialFormatDto
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 盘点单号
        /// </summary>
        public string StoreCheckCode { get; set; }

        /// <summary>
        /// 是否完成实盘，true=完成，false=未实盘
        /// </summary>
        public bool StoreCheckStatus { get; set; } = false;

        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
    }

    /// <summary>
    /// 盘点动作执行状态
    /// </summary>
    public class InventoryActionStatusDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 动作类型
        /// </summary>
        public InventoryActionType ActionType { get; set; }

        /// <summary>
        /// 动作名称
        /// </summary>
        public string ActionName { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public InventoryActionStatus Status { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTimeOffset? StartTime { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTimeOffset? CompletedTime { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public string? Result { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }
    }



    /// <summary>
    /// 完成盘点事件
    /// </summary>
    public class FinishInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 当前系统月度
        /// </summary>
        public string CurrentSysMonth { get; set; }

        /// <summary>
        /// 目标系统月度 (可选，如果不指定则自动+1个月)
        /// </summary>
        public string? TargetSysMonth { get; set; }
    }

    /// <summary>
    /// 暂存盘点创建事件
    /// </summary>
    public class CreateTinyInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }
    }

    /// <summary>
    /// 跟台盘点创建事件
    /// </summary>
    public class CreateSginyInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }
    }

    /// <summary>
    /// 盘点状态更新事件
    /// </summary>
    public class InventoryStatusUpdateEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 更新类型 (TempStore, Operation, Status)
        /// </summary>
        public string UpdateType { get; set; }

        /// <summary>
        /// 更新值
        /// </summary>
        public string UpdateValue { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 盘点操作结果
    /// </summary>
    public class InventoryOperationResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 返回数据
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// 错误详情
        /// </summary>
        public string? ErrorDetail { get; set; }
    }

    /// <summary>
    /// 换货盘点创建事件
    /// </summary>
    public class CreateExchangeInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }
    }

    /// <summary>
    /// 待确认收入盘点创建事件
    /// </summary>
    public class CreateSureIncomeInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }
    }

    /// <summary>
    /// 应收盘点创建事件
    /// </summary>
    public class CreateCreditRecordInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
    }

    /// <summary>
    /// 已签收待开票盘点创建事件
    /// </summary>
    public class CreateReceivedNoInvoiceInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
    }

    /// <summary>
    /// 应付盘点创建事件
    /// </summary>
    public class CreateDebtRecordInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
    }

    /// <summary>
    /// 付款盘点创建事件
    /// </summary>
    public class CreatePaymentRecordInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
    }

    /// <summary>
    /// 垫资盘点创建事件
    /// </summary>
    public class CreateAdvanceRecordInventoryEventDto : InventoryEventBaseDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
    }

    /// <summary>
    /// 盘点记录创建请求DTO
    /// </summary>
    public class CreateInventoryRecordRequestDto
    {
        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 盘点动作列表
        /// </summary>
        public List<InventoryActionType> ActionTypes { get; set; } = new List<InventoryActionType>();

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// 盘点记录批量处理请求DTO
    /// </summary>
    public class BatchProcessInventoryRecordRequestDto
    {
        /// <summary>
        /// 记录ID列表
        /// </summary>
        public List<Guid> RecordIds { get; set; } = new List<Guid>();

        /// <summary>
        /// 目标状态
        /// </summary>
        public InventoryRecordStatus TargetStatus { get; set; }

        /// <summary>
        /// 错误信息（状态为失败时）
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 处理人
        /// </summary>
        public string? ProcessedBy { get; set; }
    }

    /// <summary>
    /// 盘点进度查询响应DTO
    /// </summary>
    public class InventoryProgressResponseDto
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 盘点单ID
        /// </summary>
        public Guid InventoryItemId { get; set; }

        /// <summary>
        /// 总盘点类型数
        /// </summary>
        public int TotalActionTypes { get; set; }

        /// <summary>
        /// 已完成盘点类型数
        /// </summary>
        public int CompletedActionTypes { get; set; }

        /// <summary>
        /// 完成进度百分比
        /// </summary>
        public decimal ProgressPercentage => TotalActionTypes > 0 ? (decimal)CompletedActionTypes / TotalActionTypes * 100 : 0;

        /// <summary>
        /// 盘点详情列表
        /// </summary>
        public List<InventoryActionProgressDto> ActionProgress { get; set; } = new List<InventoryActionProgressDto>();

        /// <summary>
        /// 是否全部完成
        /// </summary>
        public bool IsCompleted => CompletedActionTypes == TotalActionTypes;
    }

    /// <summary>
    /// 盘点动作进度DTO
    /// </summary>
    public class InventoryActionProgressDto
    {
        /// <summary>
        /// 动作类型
        /// </summary>
        public InventoryActionType ActionType { get; set; }

        /// <summary>
        /// 动作名称
        /// </summary>
        public string ActionName { get; set; }

        /// <summary>
        /// 盘点单号
        /// </summary>
        public string? InventoryCode { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public InventoryRecordStatus Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription => Status switch
        {
            InventoryRecordStatus.Pending => "待处理",
            InventoryRecordStatus.Processing => "处理中",
            InventoryRecordStatus.Completed => "已完成",
            InventoryRecordStatus.Failed => "失败",
            _ => "未知"
        };

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedTime { get; set; }
    }

    /// <summary>
    /// 返利计提事件DTO（集成中心发布的事件格式）
    /// </summary>
    public class RebateProvisionEventDto
    {
        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }= DateTime.UtcNow;

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 事件描述
        /// </summary>
        public string Description { get; set; } = "创建返利计提";

    }

    /// <summary>
    /// 创建计提返利事件DTO（内部处理使用）
    /// </summary>
    public class CreateRebateProvisionEventDto
    {
        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string SysMonth { get; set; } = string.Empty;

        /// <summary>
        /// 触发时间
        /// </summary>
        public DateTime TriggerTime { get; set; }

        /// <summary>
        /// 计提类型：1-月末计提，2-月初冲回
        /// </summary>
        public ProvisionTypeEnum ProvisionType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 事件ID，用于幂等性控制
        /// </summary>
        public Guid EventId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTimeOffset EventTime { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// 从集成中心事件DTO创建内部事件DTO
        /// </summary>
        /// <param name="eventDto">集成中心事件DTO</param>
        /// <param name="provisionType">计提类型</param>
        /// <returns></returns>
        public static CreateRebateProvisionEventDto FromRebateProvisionEventDto(RebateProvisionEventDto eventDto, ProvisionTypeEnum provisionType)
        {
            return new CreateRebateProvisionEventDto
            {
                SysMonth = eventDto.SysMonth,
                TriggerTime = eventDto.EventTime,
                ProvisionType = provisionType,
                Remark = eventDto.Description,
                EventTime = DateTimeOffset.FromUnixTimeMilliseconds(DateTimeOffset.UtcNow.ToUnixTimeMilliseconds())
            };
        }
    }

    /// <summary>
    /// 盘点事件处理结果DTO
    /// </summary>
    public class InventoryEventResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 处理消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 处理的公司数量
        /// </summary>
        public int ProcessedCompanyCount { get; set; }

        /// <summary>
        /// 完成的公司数量（仅用于完成检查事件）
        /// </summary>
        public int? CompletedCompanyCount { get; set; }

        /// <summary>
        /// 系统月度
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 事件类型
        /// </summary>
        public string? EventType { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime ProcessTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 额外数据（可选）
        /// </summary>
        public object? AdditionalData { get; set; }
    }
}
