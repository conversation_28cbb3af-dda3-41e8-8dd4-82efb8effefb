<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <el-breadcrumb-item>进项发票多对多管理</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1" />
      <inno-crud-operation :crud="crud" v-if="activeNameStatus !== '1'">
        <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
      </inno-crud-operation>
      <inno-crud-operation :crud="crudTemporary" v-if="activeNameStatus === '1'">
        <inno-search-input v-model="crudTemporary.query.searchKey" @search="searchTemporaryCrud" />
      </inno-crud-operation>
    </div>

    <div class="app-page-body" style="padding-top: 0px">
      <inno-query-operation v-if="activeNameStatus !== '1'" :crud="crud" :query-list="queryList" />
      <inno-query-operation :crud="crudTemporary" v-if="activeNameStatus === '1'" :query-list.sync="queryTemporaryList" />
      <inno-split-pane split="horizontal" :default-percent="50">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right>
            <template #opts-left>
              <el-tabs v-model="activeNameStatus" @tab-change="search">
                <el-tab-pane :label="`待匹配发票`" name="1" />
                <el-tab-pane :label="`全部`" name="" />
                <el-tab-pane :label="`正在匹配`" name="3" />
                <el-tab-pane :label="`匹配完成`" name="4" />
                <el-tab-pane :label="`已提交`" name="99" />

              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip type="primary" v-if="activeNameStatus === '1'" @click="matchingModelShow">匹配勾稽</inno-button-tooltip>
              <inno-button-tooltip type="primary" v-if=" selectData.status === 4 && activeNameStatus !== '1'" @click="restoreModelShow">还原匹配</inno-button-tooltip>
              <inno-button-tooltip
                type="primary"
                v-if=" selectData.status === 4 && activeNameStatus !== '1'"
                :loading="submitBillLoading"
                @click="submitBill"
              >提交</inno-button-tooltip>
              <inno-button-tooltip
                type="primary"
                v-if=" selectData.status === 4  && activeNameStatus !== '1'"
                @click="editBill"
              >编辑发票明细</inno-button-tooltip>
              <inno-button-tooltip
                type="primary"
                v-if=" selectData.status === 99  && activeNameStatus !== '1'"
                :loading="cancelBillLoading"
                @click="matchingCancel"
              >取消勾稽</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>

          <el-table
            ref="tableRef"
            v-inno-loading="crud.loading"
            highlight-current-row
            v-if="activeNameStatus!=='1'"
            stripe
            border
            class="auto-layout-table"
            :data="crud.data"
            :row-class-name="crud.tableRowClassName"
            @selection-change="crud.selectionChangeHandler"
            @row-click="(e) => {
              getDetailData(e);
            }"
             :tree-props="{ children: 'originalInputBills' }"
             row-key="id"
          >
            <!-- <el-table-column fixed="left" width="55">
              <template #default="scope">
                <inno-table-checkbox :checked="scope.row.id === crud.rowData.id" />
              </template>
            </el-table-column> -->
            <!-- <el-table-column type="index" width="50" /> -->
            <el-table-column width="40" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="mergeInvoiceNumber" width="220" label="进项票合并单号" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{ scope.row.mergeInvoiceNumber }}
                </inno-button-copy>
              </template>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.mergeInvoiceNumber" :crud="crudTemporary" :column="column" />
              </template>
            </el-table-column>
            <el-table-column prop="invoiceNumber" label="发票号" width="120" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNumber }}</inno-button-copy>
              </template>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.InvoiceNumber" :crud="crud" :column="column" />
              </template>
            </el-table-column>
            <el-table-column prop="companyName" label="公司名称" min-width="110" :show-overflow-tooltip="true">
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.companyId" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="agentName" label="供应商名称" min-width="110" :show-overflow-tooltip="true">
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.agentIds" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="billTime" label="开票时间" min-width="110">
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.beginCreatedTime" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                {{
                scope.row.billTime === null
                ? ''
                : dateFormat(scope.row.billTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column prop="typeDesc" label="票据类型" />
            <el-table-column prop="invoiceCode" label="发票代码" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="purchaseDutyNumber" label="购买方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="saleDutyNumber" label="销售方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="statusDesc" label="发票状态"  min-width="100"/>

            <el-table-column sortable prop="notaxAmount" label="金额(元)" min-width="150">
              <template #default="scope">
                <inno-numeral :value="scope.row.notaxAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="taxAmount" label="税额(元)" min-width="150">
              <template #default="scope">
                <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="amount" label="总金额(元)" min-width="150">
              <template #default="scope">
                <inno-numeral :value="scope.row.amount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="submitTime" label="提交时间" min-width="110" sortable>
              <template #default="scope">{{ dateFormat(scope.row.submitTime) }}</template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建时间" min-width="110" sortable>
              <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" width="90" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column>
            <el-table-column label="是否取消勾稽" property="cancelReconciliationStatusName" width="150" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.cancelReconciliationStatusName?'是':'否' }}</template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="cancelReconciliationTime" label="取消勾稽时间" min-width="120" sortable>
              <template #default="scope">{{ dateFormat(scope.row.cancelReconciliationTime) }}</template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="110" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link
                  v-if="scope.row.invoiceNumber"
                  style="font-size: 12px"
                  type="primary"
                  @click.stop="
                    downloadFile(scope.row.invoiceNumber, scope.row.invoiceCode)
                  "
                >查看附件</el-link>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            ref="tableTemporaryRef"
            v-inno-loading="crudTemporary.loading"
            highlight-current-row
            v-if="activeNameStatus === '1'"
            stripe
            border
            class="auto-layout-table"
            :data="crudTemporary.data"
            :row-class-name="crudTemporary.tableRowClassName"
            @selection-change="crudTemporary.selectionChangeHandler"
            @row-click="(e)=>{
              getNewDetailData(e)
            }"
          >
            <el-table-column type="selection" fixed="left" width="55" />
            <el-table-column prop="invoiceNumber" label="发票号" width="120" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNumber }}</inno-button-copy>
              </template>
              <template #header="{ column }">
                <inno-header-filter :config="queryTemporaryObject.InvoiceNumber" :crud="crudTemporary" :column="column" />
              </template>
            </el-table-column>
            <el-table-column prop="companName" label="公司名称" min-width="110" :show-overflow-tooltip="true">
              <template #header="{ column }">
                <inno-header-filter :config="queryTemporaryObject.companyId" :crud="crudTemporary" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="agentName" label="供应商名称" min-width="110" :show-overflow-tooltip="true">
              <template #header="{ column }">
                <inno-header-filter :config="queryTemporaryObject.agentIds" :crud="crudTemporary" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="billTime" label="开票时间" min-width="110">
              <template #header="{ column }">
                <inno-header-filter :config="queryTemporaryObject.beginCreatedTime" :crud="crudTemporary" :column="column" />
              </template>
              <template #default="scope">
                {{
                scope.row.billTime === null
                ? ''
                : dateFormat(scope.row.billTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="票据类型" />
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建时间" min-width="110" sortable>
              <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
            </el-table-column>
            <el-table-column prop="invoiceCode" label="发票代码" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="purchaseDutyNumber" label="购买方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="saleDutyNumber" label="销售方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="statusName" label="发票状态" />

            <el-table-column sortable prop="notaxAmount" label="金额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.notaxAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="taxAmount" label="税额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="amount" label="总金额(元)" width="120">
              <template #default="scope">
                <inno-numeral :value="scope.row.amount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="是否取消勾稽" property="isCancelledReconciliation" width="150" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.isCancelledReconciliation?'是':'否' }}</template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="cancelReconciliationTime" label="取消勾稽时间" min-width="130" sortable>
              <template #default="scope">{{ dateFormat(scope.row.cancelReconciliationTime) }}</template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="110" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link
                  v-if="scope.row.invoiceNumber"
                  style="font-size: 12px"
                  type="primary"
                  @click.stop="
                    downloadFile(scope.row.invoiceNumber, scope.row.invoiceCode)
                  "
                >查看附件</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background" v-if="activeNameStatus!=='1'">
            <!-- 已选择 {{ selectData.length }} 条 -->
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud " />
          </div>
          <div class="app-page-footer background" v-if="activeNameStatus === '1'">
            已选择 {{ crudTemporary.selections.length }} 条
            <div class="flex-1" />
            <inno-crud-pagination :crud="crudTemporary" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <el-tabs v-model="activeName" class="app-page-tabs" @tab-change="activeNameChange">

            <el-tab-pane label="发票明细" name="发票明细" v-if="activeNameStatus === '1'">
              <inno-crud-operation :crud="crudDe" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs>
                    <el-tab-pane :label="``" />
                  </el-tabs>
                </template>
                <template #default>

                  <!-- <inno-button-tooltip
                    :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    "
                    type="primary"
                    @click="deleteDetail"
                  >删除</inno-button-tooltip> -->
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table
                ref="tableRefDe"
                v-inno-loading="crudDe.loading"
                class="auto-layout-table"
                highlight-current-row
                stripe
                show-summary
                :summary-method="getSummaries"
                border
                :data="crudDe.data"
                :row-class-name="crudDe.tableRowClassName"
                @selection-change="crudDe.selectionChangeHandler"
                @row-click="crudDe.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column prop="storeInItemCode" label="业务单号" min-width="180" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.storeInItemCode }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.storeInItemCode" :crud="crudDe" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productNo" label="货号" min-width="120" sortable :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductNo" :crud="crudDe" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productName" sortable label="产品名称" min-width="120" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductName" :crud="crudDe" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="model" label="型号" min-width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="producerOrderNo" label="厂家订单号" min-width="160">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.producerOrderNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" sortable label="本次入票数" width="120" class-name="isSum" />
                <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="130">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" width="140">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxAmount" sortable label="金额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="taxRate" sortable label="税率(%)" width="110" />
                <el-table-column prop="taxAmount" sortable label="税额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
                  </template>
                </el-table-column>
              </el-table>

              <div class="app-page-footer background">
                已选择 {{ crudDe.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudDe" :pageSizes="[10, 20, 50, 100, 200, 500, 1000]" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="发票明细(金蝶)" name="发票明细(金蝶)" v-if="activeNameStatus === '1'">
              <inno-crud-operation :crud="crudDeJd" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs>
                    <el-tab-pane :label="``" />
                  </el-tabs>
                </template>
                <template #default>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>

              <el-table
                ref="tableRefDeJd"
                v-inno-loading="crudDeJd.loading"
                class="auto-layout-table"
                highlight-current-row
                show-summary
                :summary-method="getSummariesJd"
                stripe
                border
                :data="crudDeJd.data"
                :row-class-name="crudDeJd.tableRowClassName"
                @selection-change="crudDeJd.selectionChangeHandler"
                @row-click="crudDeJd.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column prop="productNo" label="货号" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductNo" :crud="crudDeJd" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productName" label="产品名称" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductName" :crud="crudDeJd" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" sortable label="数量" class-name="isSum">
                  <template #default="scope">{{ scope.row.quantity }}</template>
                </el-table-column>
                <el-table-column prop="taxCost" sortable label="含税单价(元)">
                  <!-- <template #default="scope">
                    <inno-numeral :value="scope.row.taxCost" format="0,0.00" />
                  </template> -->
                </el-table-column>
                <el-table-column prop="noTaxCost" sortable label="不含税单价(元)">
                  <!-- <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.00" />
                  </template> -->
                </el-table-column>
                <el-table-column prop="noTaxAmount" sortable label="金额(元)" class-name="isSum">
                  <!-- <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxAmount" format="0,0.00" />
                  </template> -->
                </el-table-column>
                <el-table-column prop="taxRate" sortable label="税率(%)" />
              </el-table>

              <div class="app-page-footer background">
                已选择 {{ crudDeJd.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudDeJd" :pageSizes="[20, 30, 50, 100, 200, 300, 500]" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="发票明细" name="发票明细" v-if="activeNameStatus!=='1'">
              <inno-crud-operation :crud="crudNew" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs>
                    <el-tab-pane :label="``" />
                  </el-tabs>
                </template>
                <template #default>
                  <!-- <inno-button-tooltip type="primary" :ms-disabled=" crud.selections.length < 1 || crud.rowData.statusName === '已提交' "  @click="matchingModelShow">匹配勾稽</inno-button-tooltip> -->
                  <!-- <inno-button-tooltip
                    :ms-disabled="
                      crud.rowDisabled || crud.rowData.statusName === '已提交'
                    "
                    type="primary"
                    @click="deleteDetail"
                  >删除</inno-button-tooltip> -->
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>
              <el-table
                ref="tableRefDe"
                v-inno-loading="crudNew.loading"
                class="auto-layout-table"
                highlight-current-row
                stripe
                show-summary
                :summary-method="getNewSummaries"
                border
                :data="crudNew.data"
                :row-class-name="crudNew.tableRowClassName"
                @selection-change="crudNew.selectionChangeHandler"
                @row-click="crudNew.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column prop="businessCode" label="业务单号" min-width="180" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.businessCode" :crud="crudNew" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="businessTypeDescription" label="业务类型" min-width="180" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.businessTypeDescription }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="productNo" label="货号" min-width="120" sortable :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductNo" :crud="crudNew" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productName" sortable label="产品名称" min-width="120" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductName" :crud="crudNew" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="model" label="型号" min-width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="purchaseOrderCode" label="采购单号" min-width="160">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.purchaseOrderCode }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="producerOrderNo" label="厂家订单号" min-width="160">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.producerOrderNo }}</inno-button-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="matchQuantity" sortable label="本次入票数" width="120" class-name="isSum" />
                <el-table-column prop="taxCost" sortable label="含税单价(元)" min-width="130">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
                  </template>
                </el-table-column>
                <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" width="140">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="noTaxAmount" sortable label="不含税金额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxAmount" format="0,0.00" />
                  </template>
                </el-table-column> -->
                <el-table-column prop="totalAmount" sortable label="含税金额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.totalAmount" format="0,0.0000" />
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="matchedAmount" sortable label="匹配金额(元)" min-width="100" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.matchedAmount" format="0,0.00" />
                  </template>
                </el-table-column> -->
                <el-table-column prop="taxRate" sortable label="税率(%)" width="110" />
                <el-table-column prop="taxAmount" sortable label="税额(元)" min-width="100" class-name="isSum">
                  <!-- <template #default="scope">
                    <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
                  </template> -->
                </el-table-column>
              </el-table>

              <div class="app-page-footer background">
                已选择 {{ crudNew.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudNew" :pageSizes="[10, 20, 50, 100, 200, 500, 1000]" />
              </div>
            </el-tab-pane>
            <el-tab-pane label="发票明细(金蝶)" name="发票明细(金蝶)" v-if="activeNameStatus!=='1'">
              <inno-crud-operation :crud="crudNewJd" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
                <template #opts-left>
                  <el-tabs>
                    <el-tab-pane :label="``" />
                  </el-tabs>
                </template>
                <template #default>
                  <el-button @click="onFull" type="primary">
                    <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
                  </el-button>
                </template>
              </inno-crud-operation>

              <el-table
                ref="tableRefDeJd"
                v-inno-loading="crudNewJd.loading"
                class="auto-layout-table"
                highlight-current-row
                show-summary
                :summary-method="getSummariesNewJd"
                stripe
                border
                :data="crudNewJd.data"
                :row-class-name="crudNewJd.tableRowClassName"
                @selection-change="crudNewJd.selectionChangeHandler"
                @row-click="crudNewJd.singleSelection"
              >
                <el-table-column type="selection" fixed="left" width="55" />
                <el-table-column prop="productNo" label="货号":show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductNo" :crud="crudNewJd" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="productName" label="产品名称":show-overflow-tooltip="true">
                  <template #default="scope">
                    <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
                  </template>
                  <template #header="{ column }">
                    <inno-header-filter :config="queryDeObject.ProductName" :crud="crudNewJd" :column="column" />
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" sortable label="数量" class-name="isSum">
                  <template #default="scope">{{ scope.row.quantity }}</template>
                </el-table-column>
                <el-table-column prop="taxCost" sortable label="含税单价(元)">
                  <!-- <template #default="scope">
                    <inno-numeral :value="scope.row.taxCost" format="0,0.00" />
                  </template> -->
                </el-table-column>
                <el-table-column prop="noTaxCost" sortable label="不含税单价(元)" >
                  <!-- <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxCost" format="0,0.00" />
                  </template> -->
                </el-table-column>
                <el-table-column prop="noTaxAmount" sortable label="不含税金额(元)" class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.noTaxAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="totalAmount" sortable label="含税金额(元)"  class-name="isSum">
                  <template #default="scope">
                    <inno-numeral :value="scope.row.totalAmount" format="0,0.00" />
                  </template>
                </el-table-column>
                <el-table-column prop="taxRate" sortable label="税率(%)" />
              </el-table>

              <div class="app-page-footer background">
                已选择 {{ crudNewJd.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudNewJd" :pageSizes="[20, 30, 50, 100, 200, 300, 500]" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </template>
      </inno-split-pane>
    </div>
    <AgainAddInputbillDetail
      ref="againInputbill"
      :CompanyId="addCompanyId"
      :AgentId="addAgentId"
      :InputBillId="mergeInputBillId"
      :mergeInputBillDetailId="mergeInputBillDetailId"
      :showDialog="againCreatedDialogShow"
      :amount="addAmount"
      :invoiceInfo="crud.rowData"
      :kingdeeDetails="crudDeJd.data"
      @closeDialog="closeAgainDialogCallBack"
    />
    <!-- 匹配单据 -->
    <el-dialog v-model="dialogFormVisible" title="匹配单据" align-center width="530" @close="cancelMatching">
      <el-form :model="billForm" ref="refForm" :rules="rules" label-width="auto" >
        <el-form-item label="单据起止日期" prop="dates">
          <el-date-picker
            v-model="billForm.dates"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        </el-form>
      <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelMatching">取消</el-button>
        <el-button type="primary" @click="verifyMatching">
          匹配
        </el-button>
      </div>
    </template>
    </el-dialog>
    <!-- 进项发票匹配 -->
    <el-dialog v-model="tableVisible" title="匹配单据" width="95%">
      <div class="table-box">
        <div class="table-left">
          <div class="bill-box" style="margin-bottom: 13px;">
            <p class="bill-left">发票明细</p>
            <div class="bill-right">
              <span style="margin-right: 20px;">发票总金额：<inno-numeral :value="totalAmount" format="0,0.0000" /></span>
              <span>勾稽单据金额：<span style="color: #f00;"><inno-numeral :value="billAmount" format="0,0.0000" /></span> </span>
            </div>
          </div>
          <el-descriptions
            class="margin-top"
            :column="3"
            style="margin-bottom: 12px;"
            border
          >
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  合并单号
                </div>
              </template>
              <span style="width: 160px;display: inline-block;">{{ selectData.mergeInvoiceNumber }}</span>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  发票号
                </div>
              </template>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="selectData.originalInputBills?.length>0? selectData.originalInputBills?.map(item => item.invoiceNumber).join('、'):''"
                  placement="top"
                >
                  <span class="bill-item">{{ selectData.originalInputBills?.length>0? selectData.originalInputBills?.map(item => item.invoiceNumber).join('、'):''}}</span>
                </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
          <el-table
            ref="tableEditListRef"
            :data="crudEditlist.data"
            @selection-change="crudEditlist.selectionChangeHandler"
            @row-click="(e)=>{getDetailsList(e)}"
             :row-class-name="crudEditlist.tableRowClassName"
            style="width: 100%"
            border
            height="400"
            v-inno-loading="crudEditlist.loading"
            >
             <el-table-column fixed="left" width="35">
              <template #default="scope">
                <inno-table-checkbox :checked="scope.row.id === crudEditlist.rowData.id" />
              </template>
            </el-table-column>
            <el-table-column prop="productName" fixed="left" label="开票明细" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="productNo" label="规格" show-overflow-tooltip width="120">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="invoiceNumbers" label="对应发票" show-overflow-tooltip width="90">
              <template #default="scope">
                <el-button link type="primary" v-if="scope.row.invoiceNumbers.length > 1" @click.stop="checkInvoice(scope.row.invoiceNumbers)" size="small">查看发票</el-button>
                <span v-if="scope.row.invoiceNumbers.length === 1">{{ scope.row.invoiceNumbers[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" width="100" label="对应发票数量" show-overflow-tooltip/>
            <el-table-column prop="taxRate" sortable label="税率(%)" width="110" />
            <el-table-column prop="taxCost" width="120" label="含税单价" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column prop="noTaxAmount" width="120" label="不含税金额" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.noTaxAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" width="120" label="含税金额" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.totalAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column prop="matchedAmount" width="120" label="匹配金额" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.matchedAmount" format="0,0.0000" />
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
                <!-- 已选择 {{ crudEditlist.selections.length }} 条 -->
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudEditlist" :pageSizes="[20, 30, 50, 100, 200, 300, 500]" />
              </div>
        </div>
        <div class="table-right">
          <div class="bill-box">
            <p class="bill-left">匹配结果 <span v-if="dateItems.startDate !== ''&& dateItems.startDate !== null && dateItems.startDate !== undefined" class="date-item">单据起始日期：{{ dateFormat(dateItems.startDate, 'YYYY/MM/DD')}} - {{ dateFormat(dateItems.endDate, 'YYYY/MM/DD') }}</span></p>
          </div>
          <el-row :gutter="20">
        <el-col :span="8" :offset="0">
          <el-form-item label="关键字">
            <el-input
              v-model="tableForm.keyword"
              placeholder="输入关键字查询"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="5" :offset="0">
          <el-form-item label="货号">
            <el-input
              v-model="tableForm.productNo"
              placeholder="输入货号查询"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5" :offset="0">
          <el-form-item label="业务单号">
            <el-input
              v-model="tableForm.productNo"
              placeholder="输入业务单号查询"
              clearable
            ></el-input>
          </el-form-item>
        </el-col> -->
        <el-col :span="9" :offset="0">
          <el-button type="primary" size="small" @click="queryData">查询</el-button>
          <el-button type="primary" size="small" @click="againMatch">重新匹配</el-button>
          <el-button type="primary" size="small" @click="againCreatedDialog">新增</el-button>
          <el-button type="primary" size="small" @click="deleteEditData">删除</el-button>
        </el-col>
      </el-row>
          <el-table :data="crudEditData.data" ref="tableEditDataRef" style="width: 100%"
            class="auto-layout-table"
            highlight-current-row
            border
            @selection-change="crudEditData.selectionChangeHandler"
            @row-click="crudEditData.singleSelection"
            :row-class-name="crudEditData.tableRowClassName"
            show-summary
            v-inno-loading="crudEditData.loading"
            :summary-method="getSummaries"
            value-key="id"
            height="400">
            <!-- <el-table-column fixed="left" width="55">
              <template #default="scope">
                <inno-table-checkbox :checked="scope.row.id === crudEditData.rowData.id" />
              </template>
            </el-table-column> -->
            <el-table-column fixed="left" type="selection" width="55" />
            <el-table-column prop="businessCode" fixed="left" label="业务单号" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="purchaseOrderCode" label="采购单号" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseOrderCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="productNo" fixed="left" label="货号" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.productNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="产品名称" width="130" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.productName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="taxCost" label="含税单价" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.taxCost" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column prop="noTaxCost" label="不含税单价" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.noTaxCost" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column prop="matchQuantity" label="本次入票数量" width="90" show-overflow-tooltip/>
            <el-table-column prop="taxRate" label="税率(%)" width="80" show-overflow-tooltip/>
            <el-table-column prop="noTaxAmount" label="不含税金额" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.noTaxAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column prop="totalAmount" label="含税金额" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.totalAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <!-- <el-table-column prop="matchedAmount" label="匹配金额" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.matchedAmount" format="0,0.0000" />
              </template>
            </el-table-column> -->
            <el-table-column prop="businessTypeDescription" label="业务类型" width="120" show-overflow-tooltip/>
            <el-table-column prop="businessDate" label="业务日期" width="120" show-overflow-tooltip/>
            <el-table-column prop="specification" label="规格" width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.specification }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="model" label="型号" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.model }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
                已选择 {{ crudEditData.selections.length }} 条
                <div class="flex-1" />
                <inno-crud-pagination :crud="crudEditData" :pageSizes="[20, 30, 50, 100, 200, 300, 500]" />
              </div>
        </div>
      </div>
      <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeeditModel">关闭</el-button>
        <!-- <el-button type="primary" @click="verifyMatching">
          保存
        </el-button> -->
      </div>
    </template>
    </el-dialog>
    <!-- 重新匹配 -->
    <el-dialog v-model="againFormVisible" title="重新匹配" align-center @close="closeAgainFormmodel" width="530">
      <p class="tips-box">注意：重新匹配将清除以前的匹配数据，请谨慎操作！</p>
      <el-form :model="againBillForm" ref="refAgainForm" :rules="againRules" label-width="auto" >
        <el-form-item label="单据起止日期" prop="dates">
          <el-date-picker
            v-model="againBillForm.dates"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        </el-form>
      <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeAgainFormmodel">取消</el-button>
        <el-button type="primary" :loading="againMatchingLoading"  @click="verifyAgainMatching">
          重新匹配
        </el-button>
      </div>
    </template>
    </el-dialog>
    <el-dialog v-model="invoiceListVisible" title="发票列表" width="260">
    <el-table :data="invoiceList">
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column property="invoiceNo" label="进项发票号" width="200" />
      <!-- <el-table-column property="quantity" label="数量" width="100" />  -->
    </el-table>
  </el-dialog>

  </div>
</template>

<script lang="tsx" setup>
import { ElTable, ElMessage, ElMessageBox } from 'element-plus';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter, useRoute } from 'vue-router';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import AddInputbillDetail from './component/AddInputbillDetail.vue';
import AgainAddInputbillDetail from './component/AgainAddInputbillDetail.vue';
import EditInputbillDetail from './component/EditInputbillDetail.vue';
import excelImport from '@/views/financeManagement/InputBuill/component/ExcelImport.vue';
import {
  SubmitInputBill,
  DeleteBillSbumit,
  EliminatingErrors
} from '@/api/financeapi';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import { listener } from '@inno/inno-mc-vue3/lib/utils/event';
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  reactive,
  watch,
  provide
} from 'vue';
import { FormRules, FormInstance } from 'element-plus';
import request from '@/utils/request';
import Decimal from 'decimal.js';
interface InvoiceItem {
  invoiceNo: string; // 或其他具体类型
  quantity: number;
}
const activeName = ref('发票明细');
const activeIndex = ref('receive');
const route = useRoute();
const tableRef = ref<InstanceType<typeof ElTable>>();
const tableTemporaryRef = ref<InstanceType<typeof ElTable>>();
const ruleFormRef = ref(null);
const ruleTemporaryFormRef = ref(null);
let createdDialogShow = ref(false);
let againCreatedDialogShow = ref(false);
let EditcreatedDialogShow = ref(false);
let addCompanyId = ref('');
let InputBillId = ref('');
let mergeInputBillId = ref('');
let mergeInputBillDetailId = ref('');
let addAgentId = ref('');
let addAmount = ref(0);
const tableEditListRef = ref();
const tableEditDataRef = ref();
let editStoreInCodeS = ref([]);
const dialogFormVisible = ref(false);
const againFormVisible = ref(false);
const tableVisible = ref(false);
const refAgainForm = ref();
const Token = ref(window.microApp.getData().token);
const refForm = ref<FormInstance>();
const tableData:any = [];
const tableSubmitData:any = [];
const selectItems = ref();
const againMatchingLoading = ref(false);
const billForm = reactive({
  dates:[]
})
const againBillForm = reactive({
  dates:[]
})
const tableForm = reactive({
  startDate:'',
  endDate:'',
  accuracyMatching: '1',
  customerId:'',
  productNo:'',
  keyword:''
})
const invoiceListVisible = ref(false);
const invoiceList = ref<InvoiceItem[]>([]);
const totalAmount = ref(0);
const billAmount = ref(0);
const againRules = reactive<FormRules>({
  dates: [
    {
      required: true,
      message: '请选择单据起止日期',
      trigger: 'change'
    }
  ]
});
const rules = reactive<FormRules>({
  dates: [
    {
      required: true,
      message: '请选择单据起止日期',
      trigger: 'change'
    }
  ]
});
const summaryData = reactive({
  totalQuantity:  0,
  totalMatchQuantity:  0,
  totalNoTaxAmount:  0,
  totalAmount:  0,
  totalMatchedAmount:  0,
  totalTaxAmount:  0,
});
const summaryJdData = reactive({
  totalQuantity:  0,
  totalMatchQuantity:  0,
  totalNoTaxAmount:  0,
  totalAmount:  0,
  totalMatchedAmount:  0,
  totalTaxAmount:  0,
});
const dateItems = reactive({
  startDate: '',
  endDate: ''
});
const crud = CRUD(
  {
    title: '进项发票抬头',
    url: '/api/MergeInputBill/list',
    idField: 'id',
    tablekey: 'tableRef',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InvoiceNumber: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'items',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if(crud.data.length > 0){
          crud.singleSelection(crud.data[0]);
          selectData.value = crud.data[0];
          if(activeName.value === '发票明细'){
            crudNew.query = {
                mergeInputBillId: crud.data[0].id,
              };
              crudNew.toQuery();
            }else{
              crudNewJd.query = {
                mergeInputBillId: crud.data[0].id,
              };
              crudNewJd.toQuery();
            }
        }else{
          crudNew.data = [];
          crudNewJd.data = [];
        }
      }
    }
  },
  {
    table: tableRef,
    form: ruleFormRef
  }
);
const crudTemporary= CRUD(
  {
    title: '进项发票抬头',
    url: '/api/InputBillQuery/GetList',
    idField: 'id',
    tablekey: 'tableTemporaryRef',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InvoiceNumber: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if(crudTemporary.data.length > 0){
          crudTemporary.singleSelection(crudTemporary.data[0]);
          if(activeName.value === '发票明细'){
            crudDe.query = {
              searchKey: '',
              InputBillId: crudTemporary.data[0].id,
              ProductName: '',
              ProductNo: '',
              CompanName: crudTemporary.data[0].companName,
              AgentName: crudTemporary.data[0].agentName
            };
            crudDe.toQuery();
          }else{
            crudDeJd.query = {
              searchKey: '',
              InputBillId: crudTemporary.data[0].id,
              ProductName: '',
              ProductNo: '',
              CompanName: crudTemporary.data[0].companName,
              AgentName: crudTemporary.data[0].agentName
            };
            crudDeJd.toQuery();
          }
        }else{
          crudDe.data = [];
          crudDeJd.data = [];
        }
      }
    }
  },
  {
    table: tableTemporaryRef,
    form: ruleTemporaryFormRef
  }
);
const tableRefDeJd = ref<InstanceType<typeof ElTable>>();
const crudDeJd = CRUD(
  {
    title: '进项发票金蝶详情',
    url: '/api/InputBillQuery/GetDetailJdList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InputBillId: '',
      ProductName: '',
      ProductNo: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadDetailSumJd(crudDeJd.query.InputBillId, crudDeJd.query.CompanName, crudDeJd.query.AgentName);
      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDeJd
  }
);
const crudNewJd = CRUD(
  {
    title: '进项发票金蝶详情',
    url: '/api/MergeInputBill/getMergeInputBillDetail',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InputBillId: '',
      ProductName: '',
      ProductNo: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total',
      summary: 'summary' // 添加合计数据字段
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud,data) => {
        // 在刷新后检查是否有合计数据
        if(data.summary){
          summaryJdData.totalAmount = data.summary.totalAmount;
          summaryJdData.totalMatchQuantity = data.summary.totalMatchQuantity;
          summaryJdData.totalMatchedAmount = data.summary.totalMatchedAmount;
          summaryJdData.totalNoTaxAmount = data.summary.totalNoTaxAmount;
          summaryJdData.totalQuantity = data.summary.totalQuantity;
          summaryJdData.totalTaxAmount = data.summary.totalTaxAmount;
        }
      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDeJd
  }
);
const tableRefDe = ref<InstanceType<typeof ElTable>>();
const crudDe = CRUD(
  {
    title: '进项发票提交详情',
    url: '/api/InputBillQuery/GetDetailList',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InputBillId: '',
      ProductName: '',
      ProductNo: '',
      CompanName: '',
      AgentName: '',
      StoreInItemCode: ''
    },

    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {

      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDe
  }
);
const crudNew = CRUD(
  {
    title: '进项发票提交详情',
    url: '/api/MergeInputBill/getSubmitDetails',
    idField: 'id',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InputBillId: '',
      ProductName: '',
      ProductNo: '',
      CompanName: '',
      AgentName: '',
      StoreInItemCode: ''
    },

    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total',
      summary: 'summary' // 添加合计数据字段
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud,data) => {
        // 在刷新后检查是否有合计数据
        console.log('合计数据:============', data)
        if(data.summary){
          summaryData.totalAmount = data.summary.totalAmount;
          summaryData.totalMatchQuantity = data.summary.totalMatchQuantity;
          summaryData.totalMatchedAmount = data.summary.totalMatchedAmount;
          summaryData.totalNoTaxAmount = data.summary.totalNoTaxAmount;
          summaryData.totalQuantity = data.summary.totalQuantity;
          summaryData.totalTaxAmount = data.summary.totalTaxAmount;
        }
      }
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    }
  },
  {
    table: tableRefDe
  }
);
const crudEditData = CRUD(
  {
    title: '进项发票抬头',
    url: '/api/MergeInputBill/getSubmitDetails',
    idField: 'id2',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InvoiceNumber: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if(crud.data.length){
        //   crud.singleSelection(crud.data[0]);
        //   selectData.value = crud.data[0];
        // crudNewJd.query = {
        //   mergeInputBillId: crud.data[0].id,
        // };
        // crudNew.query = {
        //   mergeInputBillId: crud.data[0].id,
        // };
        // crudNewJd.toQuery();
        // crudNew.toQuery();
        }
      }
    }
  },
  {
    table: tableEditDataRef,
    form: ruleFormRef
  }
);
const crudEditlist = CRUD(
  {
    title: '进项发票抬头',
    url: '/api/MergeInputBill/GetMergeInputBillDetail',
    idField: 'id2',
    sort: ['createdTime,desc'],
    query: {
      searchKey: '',
      InvoiceNumber: '',
      CompanName: '',
      AgentName: ''
    },
    method: 'post',
    userNames: ['createdBy', 'updatedBy', 'disabledBy'],
    optShow: {
      exportCurrentPage: false // 为false则不会显示导出当前页
    },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    page: {
      // 页码
      page: 1,
      // 每页数据条数
      size: 10,
      // 总数据条数
      total: 0
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        //默认选中第一行
        if(crud.data.length){
        //   crud.singleSelection(crud.data[0]);
        //   selectData.value = crud.data[0];
        // crudNewJd.query = {
        //   mergeInputBillId: crud.data[0].id,
        // };
        // crudNew.query = {
        //   mergeInputBillId: crud.data[0].id,
        // };
        // crudNewJd.toQuery();
        // crudNew.toQuery();
        }
      }
    }
  },
  {
    table: tableEditListRef,
    form: ruleFormRef
  }
);
  let sumJd = ref({
    NumberSum: 0,
    ValueSum: 0
  });
  let sum = ref({
    NumberSum: 0,
    ValueSum: 0,
    TaxValueSum: 0,
  });
  const excelImportAction = ref(gatewayUrl + 'v1.0/finance-backend/api/InputBillExecute/ExportInvoiceForBusinessBill')



const datatypelist = [
  { type: 0, name: '全部' },
  { type: 1, name: '普票' },
  { type: 2, name: '专票' }
];
const Statustypelist = [
  { type: '0', name: '全部' },
  { type: '1', name: '临时发票' },
  { type: '2', name: '已提交' }
];
  const hasDetaillist = [
    { type: 0, name: '全部' },
    { type: 1, name: '是' },
    { type: 2, name: '否' }
  ];

const queryList = computed(() => [
  {
    key: 'mergeInvoiceNumber',
    label: '合并单号',
    show: true
  },
  {
    key: 'InvoiceNumber',
    label: '发票号',
    show: true
  },
  {
    show: true,
    key: 'companyId',
    label: '公司名称',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      functionUri: 'metadata://inventory/store-in/query/list'
    }
  },
  {
    key: 'storeInItemCode',
    label: '业务单号',
    show: true
  },
  {
    show: true,
    key: 'agentIds',
    label: '供应商名称',
    type: 'remoteSelect',
    multiple: true,
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      functionUri: 'metadata://inventory/store-in/query/list'
    }
  },
  {
    key: 'model',
    label: '型号',
    show: true
  },
  {
    key: 'Type',
    label: '票据类型',
    type: 'select',
    labelK: 'name',
    valueK: 'type',
    dataList: datatypelist,
    show: true
  },
  {
    key: 'hasDetail',
    label: '是否已有发票明细',
    type: 'select',
    labelK: 'name',
    valueK: 'type',
    dataList: hasDetaillist,
    show: true
  },
  {
    key: 'submitTimeStart',
    endDate: 'submitTimeEnd',
    label: '提交时间',
    type: 'daterange',
    format: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'beginCreatedTime',
    endDate: 'endCreatedTime',
    label: '开票时间',
    type: 'daterange',
    format: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'productNo',
    label: '货号',
    show: true
  },
  {
    key: 'createdByList',
    label: '创建人',
    method: 'post',
    multiple: true,
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
    placeholder: '用户名称搜索',
    valueK: 'name',
    labelK: 'displayName',
    props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
    slots: {
        option: ({ item }) => (
        <>
          <span>{item.displayName}</span>
          <span style="float:right">{item.name}</span>
        </>
      )
      }
  },
  {
  key: 'cancelReconciliationStatusName',
  label: '是否取消勾稽',
  type: 'select',
  labelK: 'label',
  valueK: 'value',
  dataList: [
    {
      label: '是',
      value: true
    },
    {
      label: '否',
      value: false
    }
  ],
  show: false
  },
  {
    key: 'beginCancelReconciliationTime',
    endDate: 'endCancelReconciliationTime',
    label: '取消勾稽时间',
    type: 'daterange',
    format: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
]);
const queryTemporaryList = computed(() => [
  {
    key: 'InvoiceNumber',
    label: '发票号',
    show: true
  },
  {
    show: true,
    key: 'companyId',
    label: '公司名称',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      functionUri: 'metadata://inventory/store-in/query/list'
    }
  },
  {
    key: 'storeInItemCode',
    label: '业务单号',
    show: true
  },
  {
    show: true,
    key: 'agentIds',
    label: '供应商名称',
    type: 'remoteSelect',
    multiple: true,
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
    labelK: 'name',
    valueK: 'id',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      functionUri: 'metadata://inventory/store-in/query/list'
    }
  },
  // {
  //   key: 'CompanName',
  //   label: '公司名称',
  //   show: true
  // },
  // {
  //   key: 'AgentName',
  //   label: '供应商名称',
  //   show: true
  // },
  {
    key: 'Type',
    label: '票据类型',
    type: 'select',
    labelK: 'name',
    valueK: 'type',
    dataList: datatypelist,
    show: true
  },
  {
    key: 'hasDetail',
    label: '是否已有发票明细',
    type: 'select',
    labelK: 'name',
    valueK: 'type',
    dataList: hasDetaillist,
    show: true
  },
  // {
  //   key: 'Status',
  //   label: '发票状态',
  //   type: 'select',
  //   labelK: 'name',
  //   valueK: 'type',
  //   dataList: Statustypelist,
  //   show: true
  // },
  {
    key: 'beginCreatedTime',
    endDate: 'endCreatedTime',
    label: '开票时间',
    type: 'daterange',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
  {
    key: 'productNo',
    label: '货号',
    show: true
  },
  {
  key: 'cancelReconciliationStatusName',
  label: '是否取消勾稽',
  type: 'select',
  labelK: 'label',
  valueK: 'value',
  dataList: [
    {
      label: '是',
      value: true
    },
    {
      label: '否',
      value: false
    }
  ],
  show: false
  },
  {
    key: 'beginCancelReconciliationTime',
    endDate: 'endCancelReconciliationTime',
    label: '取消勾稽时间',
    type: 'daterange',
    format: 'YYYY-MM-DD',
    defaultTime: [
      new Date(2000, 1, 1, 0, 0, 0),
      new Date(2000, 2, 1, 23, 59, 59)
    ],
    show: true
  },
]);
// 按Excel导入
const ExcelImportVisibel = ref(false);
const ExcelImportHandler = () => {
  ExcelImportVisibel.value = true;
};
// 批量导入
const BatchExcelImportVisibel = ref(false);
const BatchExcelImportHandler = () => {
  BatchExcelImportVisibel.value = true;
};

const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
const queryTemporaryObject = computed(() =>
  Object.fromEntries(queryTemporaryList.value.map((item) => [item.key, item]))
);
const queryDeList = computed(() => [
  {
    key: 'ProductName',
    label: '产品名称',
    show: true
  },
  {
    key: 'ProductNo',
    label: '货号',
    show: true
  },
  {
    key: 'StoreInItemCode',
    label: '入库单',
    show: true
  }
]);
const queryDeObject = computed(() =>
  Object.fromEntries(queryDeList.value.map((item) => [item.key, item]))
);
onMounted(() => {
  // crud.toQuery();
  crudTemporary.query.Status = '1';
  crudTemporary.toQuery();
  // 表头拖拽必须在这里执行
  tableDrag(tableRef);
  tableDrag(tableRefDe);
  tableDrag(tableRefDeJd);
});
let detailLable = ref('业务单号');

watch(
  () => route.query.invoiceNo,
  (n, o) => {
    if (n != null) {
      crud.query.invoiceNumber = n;
      crud.toQuery();
    }
  },
  { deep: true }
);
const ishl = ref(0);
const ishf = ref(0);
watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      ishl.value = 0;
      ishf.value = 0;
      crud.selections.forEach((item) => {
        if (item.statusName === '已忽略') {
          ishl.value += 1;
        } else if (item.statusName === '临时发票') {
          ishf.value += 1;
        } else {
          // continue
        }
      });
    }
  },
  { deep: true }
);
const eliminatingErrorsLoading = ref(false);

// 按单据导入内容
const importReceiptContent = ref();
// 按单据导入dialog
const inReceiptImportVisible = ref(false);
// 按单据导入
const inReceiptImport = () => {
  importReceiptContent.value = '';
  inReceiptImportVisible.value = true;
};
// 按单据导入确定
const inReceiptImportOkLoading = ref(false);

//是否显示弹窗
const createdDialog = () => {
  createdDialogShow.value = true;
};

const addInputbill = ref(null);
const againInputbill = ref();


let editProductNos = ref([]);
const editDetail = async () => {
  if (crudDe.selections.length != 1) {
    ElMessage({
      showClose: true,
      message: '请选择要操作的数据,最多一条数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  var sumData = [];
  var producNo = [];
  for (let index = 0; index < crudDe.selections.length; index++) {
    const element = crudDe.selections[index];
    sumData = sumData.concat(element.storeInItemCode);
    producNo = producNo.concat(element.productNo);
  }
  editStoreInCodeS.value = sumData;
  editProductNos.value = producNo;
  EditcreatedDialogShow.value = true;
};
const deleteDetail = async () => {
  if (crudDe.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请选择要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  var sumData = [];
  var detailIds = [];
  for (let index = 0; index < crudDe.selections.length; index++) {
    const element = crudDe.selections[index];
    detailIds.push(crudDe.selections[index].id.toUpperCase());
  }
  var selectRow = crud.selections.map((item) => {
    return item['id'];
  });
  if (selectRow.length > 1) {
    ElMessage({
      showClose: true,
      message: '提交时只能选择一条数据',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  console.log(sumData);
  console.log(detailIds);
  DeleteBillSbumit(sumData, selectRow[0].toUpperCase(), detailIds)
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage({
          showClose: true,
          message: '删除成功！',
          type: 'success',
          duration: 3 * 1000
        });
        //crud.toQuery();
        crudDe.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
const submitBillLoading = ref(false);
const submitBill = async () => {
  if (selectData.value?.id) {
    // 如果已经在加载中，防止重复点击
    if (submitBillLoading.value) {
      return;
    }

    submitBillLoading.value = true;
    ElMessageBox.confirm('是否确认提交数据！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(()=>{
     request({
      url: '/api/MergeInputBill/submitMatch',
      method: 'POST',
      data: {
        mergeInputBillId: selectData.value?.id
      }
    }) .then((res) => {
      if (res.data.code == 200) {
        ElMessage({
          showClose: true,
          message: '提交成功！',
          type: 'success',
          duration: 3 * 1000
        });
        crud.toQuery();
        //crudDe.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      submitBillLoading.value = false;
    });
  }).catch(() => {
    // 取消操作时也需要重置loading状态
    submitBillLoading.value = false;
  })
  }else{
    ElMessage({
      showClose: true,
      message: '请选择需要提交的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
};
// 忽略发票
const ignoreBill = async () => {
  if (crud.selections.length < 1) {
    ElMessage({
      showClose: true,
      message: '请至少选择一条要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  let ids = crud.selections.map((item) => {
    return item['id'];
  });
  ElMessageBox.confirm('只能忽略临时发票，确定要将发票忽略?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    submitBillLoading.value = true;
    request({
      url: '/api/InputBillExecute/IgnoreBill',
      method: 'POST',
      data: ids
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        submitBillLoading.value = false;
      });
  });
};
// 恢复发票
const restoreBill = async () => {
  if (crud.selections.length < 1) {
    ElMessage({
      showClose: true,
      message: '请至少选择一条要操作的数据！',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  let ids = crud.selections.map((item) => {
    return item['id'];
  });
  ElMessageBox.confirm('只能恢复已忽略的发票，确定要将发票恢复?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    submitBillLoading.value = true;
    request({
      url: '/api/InputBillExecute/RestoreBill',
      method: 'POST',
      data: ids
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        submitBillLoading.value = false;
      });
  });
}
const editInputbill = ref(null);
//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
      // 如果没有后端返回的合计数据，使用前端计算
      if (column.property == 'quantity') {
        const values = data.map((item) => item.quantity || 0);
        const total = values.reduce(
          (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
          new Decimal(0)
        );
        sums[index] = total;
      } else if(column.property == 'matchQuantity'){
        const values = data.map((item) => item.matchQuantity || 0);
        const total = values.reduce(
          (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
          new Decimal(0)
        );
        sums[index] = total;
      }else if (column.property == 'noTaxAmount') {
        const values = data.map((item) => item.noTaxAmount || 0);
        const total = values.reduce(
          (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
          new Decimal(0)
        );
        sums[index] = asyncNumeral(total, '0,0.0000');
      } else if (column.property == 'totalAmount') {
        const values = data.map((item) => item.totalAmount || 0);
        const total = values.reduce(
          (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
          new Decimal(0)
        );
        sums[index] = asyncNumeral(total, '0,0.0000');
      } else if (column.property == 'matchedAmount') {
        const values = data.map((item) => item.matchedAmount || 0);
        const total = values.reduce(
          (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
          new Decimal(0)
        );
        sums[index] = asyncNumeral(total, '0,0.0000');
      } else if (column.property == 'taxAmount') {
        const values = data.map((item) => item.taxAmount || 0);
        const total = values.reduce(
          (prev, curr) => new Decimal(prev).add(new Decimal(curr)),
          new Decimal(0)
        );
        sums[index] = asyncNumeral(total, '0,0.0000');
      }else {
        sums[index] = '';
      }
  });
  return sums;
};
const getNewSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: any[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    // 如果有后端返回的合计数据，优先使用
      if (column.property == 'quantity' && summaryData.totalQuantity !== undefined) {
        sums[index] = summaryData.totalQuantity;
      } else if (column.property == 'matchQuantity' && summaryData.totalMatchQuantity !== undefined) {
        sums[index] = summaryData.totalMatchQuantity;
      } else if (column.property == 'noTaxAmount' && summaryData.totalNoTaxAmount !== undefined) {
        sums[index] = asyncNumeral(summaryData.totalNoTaxAmount, '0,0.0000');
      } else if (column.property == 'totalAmount' && summaryData.totalAmount !== undefined) {
        sums[index] = asyncNumeral(summaryData.totalAmount, '0,0.0000');
      } else if (column.property == 'matchedAmount' && summaryData.totalMatchedAmount !== undefined) {
        sums[index] = asyncNumeral(summaryData.totalMatchedAmount, '0,0.0000');
      } else if (column.property == 'taxAmount' && summaryData.totalTaxAmount !== undefined) {
        sums[index] = asyncNumeral(summaryData.totalTaxAmount, '0,0.0000');
      } else {
        sums[index] = '';
      }
  });
  return sums;
};

  const getSummariesNewJd = (param: SummaryMethodProps) => {
    const { columns, data } = param;
    const sums: string[] = [];
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '总合计';
        return;
      }
      if (column.property == 'quantity' && summaryJdData.totalQuantity !== undefined) {
        sums[index] = summaryJdData.totalQuantity;
      } else if (column.property == 'noTaxAmount' && summaryJdData.totalNoTaxAmount !== undefined) {
        sums[index] = asyncNumeral(summaryJdData.totalNoTaxAmount, '0,0.0000');
      } else if (column.property == 'totalAmount' && summaryJdData.totalAmount !== undefined) {
        sums[index] = asyncNumeral(summaryJdData.totalAmount, '0,0.0000');
      } else if (column.property == 'taxAmount' && summaryJdData.totalTaxAmount !== undefined) {
        sums[index] = asyncNumeral(summaryJdData.totalTaxAmount, '0,0.0000');
      } else {
        sums[index] = '';
      }
    });
    return sums;
  };
  const getSummariesJd = (param: SummaryMethodProps) => {
    const { columns, data } = param;
    const sums: string[] = [];
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '总合计';
        return;
      }
      if (column.property == 'quantity') {
        sums[index] = sumJd.value.numberSum
        sums[index] = rbstateFormat(sums[index], column.property);
      } else if (column.property == 'noTaxAmount') {
        sums[index] = sumJd.value.valueSum
      }  else {
        sums[index] = '';
      }

    });
    return sums;
  };
// 合计金额千分位
const rbstateFormat = (cellValue, property) => {
  return asyncNumeral(
    cellValue,
    property === 'quantity' ? '0,0.0000000000' : '0,0.00'
  );
};
const activeNameStatus = ref('1');
const search = (val:any) => {
  if (val === '1') {
    crudTemporary.query.Status = val;
    crudTemporary.toQuery();
  } else {
    crud.query.Status = val;
    crud.toQuery();
  }

};
const searchCrud = () => {
  activeNameStatus.value = '';
  crud.query.Status = '';
  crud.toQuery();
};
const searchTemporaryCrud = () => {
  activeNameStatus.value = '1';
  crudTemporary.query.Status = '1';
  crudTemporary.toQuery();
};
const downloadFile = (invoiceNo, invoiceCode) => {
  request({
    url:
      '/api/InputBillQuery/GetKDFilePath?invoiceNo=' +
      invoiceNo +
      '&invoiceCode=' +
      invoiceCode,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          FileViewer.show(
            res.data.data.map((i) => i.previewAddress), // 可以为数组和逗号隔开的字符串
            0, // 默认打开的下标
            {} // FileViewer props
          );
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶附件，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};
const download = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      if (res.data.code === 500) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        return false;
      }
      const filename = fileName || '';
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; //'application/vnd.ms-excel';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((t) => {});
};
  const excelImportActionRef = ref()
  const confirmSubmitHandler = async () => {
    // 先保留默认的地址
    let oldAction = Object.assign(excelImportAction.value)
    excelImportAction.value = gatewayUrl + 'v1.0/finance-backend/api/InputBillExecute/ExportInvoiceForBusinessBillAndSubmit' // 改为临时提交地址
    // 导入
    await excelImportActionRef.value.uploadRefSubmit()
    // 再修改为默认地址
    excelImportAction.value = oldAction
  }
// 按Excel导入成功后的回调

// 按Excel导入成功后的回调
  const errbtn = ref(false);
  const errFileId = ref('');
// 下载错误信息文件
const downloadErrInfoExcel = () => {
  handleDownload(errFileId.value);
};
const handleDownload = (id) => {
  request({
    url: window.gatewayUrl + 'api/FileDownload/code',
    method: 'get'
  }).then((res) => {
    if (res.data.code) {
      window.open(
        window.gatewayUrl +
          'api/FileDownload/temp/direct?&fileId=' +
          id +
          '&code=' +
          res.data.code
      );
    }
  });
};
//导出数据
const exportBillLoading = ref(false);
const exportBill = () => {
  ElMessageBox.confirm('是否导出符合条件的所有数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    exportBillLoading.value = true;
    return request({
      url: '/api/InputBillQuery/ExportBill',
      data: crud.query,
      method: 'POST',
      dataType: 'json',
      headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
      responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
    })
      .then((res) => {
        const xlsx =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
        const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
        a.download = '导出进项发票文件' + new Date().getTime() + '.xlsx';
        a.href = window.URL.createObjectURL(blob);
        a.click();
        a.remove();
      })
      .catch((err) => {
        throw '请求错误';
      })
      .finally(() => {
        exportBillLoading.value = false;
      });
  });
};
// 取消勾稽
const cancelBillLoading = ref(false);
const cancelBill = () => {
  console.log(JSON.stringify(crud.rowData))
  ElMessageBox.confirm('确认取消勾稽此发票', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    cancelBillLoading.value = true;
    request({
      url: '/api/InputBillExecute/CancelBill?id='+crud.rowData.id,
      method: 'POST'
    })
      .then((res) => {
        if (res.data.code == 200) {
          ElMessage({
            showClose: true,
            message: '操作成功！',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        cancelBillLoading.value = false;
      });
  });
}
const tabhandleClick = (tabName) => {
  if (tabName == 'recognize') {
    crud.toQuery();
  } else if (tabName == 'receive') {
    // crudReceive.toQuery();
  }
};
const matchingModelShow = () =>{
  billForm.dates = [];
  dialogFormVisible.value = true;
}
const verifyMatching = () =>{
  if (!refForm.value) return;
  refForm.value.validate((valid, field) => {
    if (valid) {
      if(crudTemporary.selections.length < 1){
        ElMessage({
          showClose: true,
          message: '请选择需要匹配勾稽的发票。',
          type: 'warning',
          duration: 3 * 1000
        });
        return;
      }
      let postData = {
        startDate: billForm.dates[0],
        endDate: billForm.dates[1],
        inputBillIds: crudTemporary.selections.map(item => item.id)
      }
      request({
      url: '/api/MergeInputBill/create',
      data:postData,
      method: 'post'
    }).then((res) => {
      if(res.data.code === 200){
        dialogFormVisible.value = false;
        billForm.dates = [];
        ElMessage({
          showClose: true,
          message: `合并进项发票成功,单号${res.data.data.mergeInvoiceNumber}，系统正在匹配，匹配完成后将发消息通知。`,
          type: 'success',
          duration: 3 * 1000
        });
        // crud.toQuery();
        crudTemporary.selections = []; // 清空选中的临时发票
        // 重新加载临时发票列表数据
        crudTemporary.query.Status = '1';
        crudTemporary.toQuery();
      }else{
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    }).catch((err) => {
      ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
    })

      // tableVisible.value = true;

    }
  })

}
// 重新匹配
const verifyAgainMatching= () =>{
  if (!refAgainForm.value) return;
  refAgainForm.value.validate((valid, field) => {
    if (valid) {
      againMatchingLoading.value = true;
      let postData = {
        startDate: againBillForm.dates[0],
        endDate: againBillForm.dates[1],
        mergeInputBillId: selectData.value?.id,
        isReload: true
      }
      request({
      url: '/api/MergeInputBill/startAsyncMatch',
      data:postData,
      method: 'post'
    }).then((res) => {
      if(res.data.code === 200){
        tableVisible.value = false;
        againFormVisible.value = false;
        againMatchingLoading.value = false;
        againBillForm.dates = [];
        ElMessage({
          showClose: true,
          message: `重新匹配成功，原有匹配记录将被删除，请等待匹配结果。`,
          type: 'success',
          duration: 3 * 1000
        });
        crud.toQuery();
      }else{
        againMatchingLoading.value = false;
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    }).catch((err) => {
      againMatchingLoading.value = false;
      ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
    })

      // tableVisible.value = true;

    }
  })

}
const againCreatedDialog = () => {
  // againCreatedDialogShow.value = true;
  if(crudEditlist.rowData.id){
    againInputbill.value.getAddList(crudEditlist.rowData.mergeInputBillId);
  }else{
    ElMessage({
      showClose: true,
      message: '请选择发票明细。',
      type: 'warning',
      duration: 3 * 1000
    });
  }

};
const closeAgainDialogCallBack = () => {
  againCreatedDialogShow.value = false;

    crudEditlist.query = {
      mergeInputBillId: selectData.value?.id
    };

    crudEditlist.toQuery().then(()=>{
      let items = crudEditlist.data.find(el=>el.id === selectItems.value);
      if(items){
        crudEditlist.singleSelection(items);
      }

      if(selectItems.value){
        crudEditData.query = {
          mergeInputBillId: selectData.value?.id,
          MergeInputBillDetailId: selectItems.value,
          keyword:tableForm.keyword
        };
        crudEditData.toQuery();
      }else{
        crudEditData.query = {
          mergeInputBillId: selectData.value?.id,
          keyword:tableForm.keyword
        };
        crudEditData.toQuery();
      }
  });
    getInvoiceAmount();

};
const editBill = () => {
  if(selectData.value?.id && selectData.value?.mergeInvoiceNumber ){
    // tableVisible.value = true;
    crudEditData.query = {
        mergeInputBillId: selectData.value?.id,
        keyword:tableForm.keyword
      };
    crudEditlist.query = {
      mergeInputBillId: selectData.value?.id
    };
    crudEditData.toQuery();
    crudEditlist.toQuery().then((res)=>{
      if(crudEditlist.data && crudEditlist.data.length > 0){
        tableVisible.value = true;
      }
    });
    getInvoiceAmount();
    getCachedMatchCondition( selectData.value?.id);
  }else{
    ElMessage({
      showClose: true,
      message: '请选择需要编辑的数据进项票合并单进行操作。',
      type: 'warning',
      duration: 3 * 1000
    });
  }

 }
 const getInvoiceAmount = () =>{
  request({
      url: '/api/MergeInputBill/getInvoiceAmountStatistics',
      data: {
        mergeInputBillId: selectData.value?.id
      },
      method: 'post'
    }).then((res) => {
      if(res.data.code === 200){
        totalAmount.value = res.data.data.totalInvoiceAmount;
        billAmount.value = res.data.data.totalSubmitAmount;
      }else{
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    }).catch((err) => {
      ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
    })
 }
 const selectData = ref({})
 const getDetailData = (e) => {
  crud.singleSelection(e);
  if(e){
    selectData.value = e
    if(activeName.value === '发票明细'){
      crudNew.query = {
          mergeInputBillId: e.id,
        };
        crudNew.toQuery();
      }else{
        crudNewJd.query = {
          mergeInputBillId: e.id,
        };
        crudNewJd.toQuery();
      }
  }
};
const getNewDetailData = (e) => {
  if(e){
    if(activeName.value === '发票明细'){
      crudDe.query = {
        searchKey: '',
        InputBillId: e.id,
        ProductName: '',
        ProductNo: '',
        CompanName: e.companName,
        AgentName: e.agentName
      };
      crudDe.toQuery();
    }else{
      crudDeJd.query = {
        searchKey: '',
        InputBillId: e.id,
        ProductName: '',
        ProductNo: '',
        CompanName: e.companName,
        AgentName: e.agentName
      };
      crudDeJd.toQuery();
    }
  }

  crud.singleSelection(e);
};
const getDetailsList = (e:any) =>{
  console.log('点击行事件赋值并调用详细信息=========', e)
  crudEditlist.singleSelection(e);
  if(crudEditlist.rowData.id){
    selectItems.value = e.id;
    mergeInputBillId.value = e.mergeInputBillId;
    mergeInputBillDetailId.value = e.id;
    crudEditData.query = {
        mergeInputBillId: crudEditlist.rowData.mergeInputBillId,
        MergeInputBillDetailId: crudEditlist.rowData.id,
        keyword:tableForm.keyword
      };
      crudEditData.toQuery();
  }else{
    selectItems.value = null;
    crudEditData.query = {
        mergeInputBillId: selectData.value?.id,
        MergeInputBillDetailId: null,
        keyword:tableForm.keyword
      };
      crudEditData.toQuery();
  }

}
const queryData = () => {
  crudEditData.query = {
    mergeInputBillId: selectData.value?.id,
    MergeInputBillDetailId: crudEditlist.rowData.id,
    keyword:tableForm.keyword
  };
  crudEditData.toQuery();
}
const deleteEditData = () =>{
  if(crudEditData.selections.length > 0 ){
    ElMessageBox.confirm('确定删除该数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: '/api/MergeInputBill/deleteMatch',
        method: 'post',
        data: {
          mergeInputBillId: selectData.value?.id,
          matchDetailIds: crudEditData.selections.map(item => item.id),
        }
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '删除成功',
            type: 'success',
            duration: 3 * 1000
          });
          crudEditlist.query = {
            mergeInputBillId: selectData.value?.id
          };
          crudEditlist.toQuery().then(()=>{
            let items = crudEditlist.data.find(el=>el.id === selectItems.value);
            if(items){
              crudEditlist.singleSelection(items);
            }
          });
          crudEditData.query = {
            mergeInputBillId: crudEditlist.rowData.mergeInputBillId?crudEditlist.rowData.mergeInputBillId:selectData.value?.id,
            MergeInputBillDetailId: crudEditlist.rowData.id,
            keyword:tableForm.keyword
          };
          crudEditData.toQuery();
          getInvoiceAmount();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      }).catch((err) => {
        ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
      })

    })
  }else{
    ElMessage({
      showClose: true,
      message: '请选择需要删除的数据！',
      type: 'error',
      duration: 3 * 1000
    });
  }
}
const restoreModelShow = () => {
  if(selectData.value?.id){
    ElMessageBox.confirm('确定还原该数据吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: '/api/MergeInputBill/restore',
        method: 'post',
        data: {
          mergeInputBillId: selectData.value?.id,
        }
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '还原成功',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      }).catch((err) => {
        ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
      })

    })
  }else{
    ElMessage({
      showClose: true,
      message: '请选择需要还原的数据！',
      type: 'error',
      duration: 3 * 1000
    });
  }
}
const againMatch = () => {
  againFormVisible.value =true;
}
const checkInvoice = (list:any) =>{

  if(list.length > 0){
    invoiceList.value = [];
    list.map((el:any) => {
      invoiceList.value.push({
        invoiceNo: el,
        quantity:1
      })
    })
    invoiceListVisible.value = true;
  }
}
const cancelMatching = () =>{
  billForm.dates = []; // 清空日期选择
  // crudTemporary.selections = []; // 清空选中的临时发票
  // crudTemporary.data = []; // 清空列表数据
  dialogFormVisible.value = false;
}
  // 监听消息在组件使用，组件销毁后会自动取消监听
  listener('notification', ({ detail }) => {
    console.log('notification', detail); // 消息内容。可根据消息内容判断处理自己的业务逻辑
    if (detail.type === '1' && detail.message.indexOf('进项票多对多匹配完成') > -1) {
      crud.toQuery();
    }
  });
  const matchingCancel = () => {
    if(selectData.value?.id){
    // 如果已经在加载中，防止重复点击
    if (cancelBillLoading.value) {
      return;
    }

    cancelBillLoading.value = true;
    ElMessageBox.confirm('取消勾稽成功后，如果需要修改发票明细数据，需要重新匹配数据，确定取消勾稽吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      request({
        url: '/api/MergeInputBill/RevokeMatch',
        method: 'post',
        data: {
          mergeInputBillId: selectData.value?.id,
          mergeInvoiceNumber: selectData.value?.mergeInvoiceNumber,
        }
      }).then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '取消成功',
            type: 'success',
            duration: 3 * 1000
          });
          crud.toQuery();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      }).catch((err) => {
        ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
      }).finally(() => {
        cancelBillLoading.value = false;
      })
    }).catch(() => {
      // 取消操作时也需要重置loading状态
      cancelBillLoading.value = false;
    })
  }else{
    ElMessage({
      showClose: true,
      message: '请选择需要取消匹配的数据！',
      type: 'error',
      duration: 3 * 1000
    });
  }
  }
const activeNameChange = (val:any) =>{
  if(activeNameStatus.value === '1'){
    if(activeName.value === '发票明细'){
      crudDe.query = {
        searchKey: '',
        InputBillId: crudTemporary.rowData.id,
        ProductName: '',
        ProductNo: '',
        CompanName: crudTemporary.rowData.companName,
        AgentName: crudTemporary.rowData.agentName
      };
      crudDe.toQuery();
    }else{
      crudDeJd.query = {
        searchKey: '',
        InputBillId: crudTemporary.rowData.id,
        ProductName: '',
        ProductNo: '',
        CompanName: crudTemporary.rowData.companName,
        AgentName: crudTemporary.rowData.agentName
      };
      crudDeJd.toQuery();
    }
  }else{
    if(activeName.value === '发票明细'){
    crudNew.query = {
        mergeInputBillId: selectData.value.id,
      };
      crudNew.toQuery();
    }else{
      crudNewJd.query = {
        mergeInputBillId: selectData.value.id,
      };
      crudNewJd.toQuery();
    }
  }
}
const closeeditModel = () => {
  tableVisible.value = false
  if(activeName.value === '发票明细'){
    crudNew.query = {
        mergeInputBillId: selectData.value.id,
      };
      crudNew.toQuery();
    }else{
      crudNewJd.query = {
        mergeInputBillId: selectData.value.id,
      };
      crudNewJd.toQuery();
    }
}
const closeAgainFormmodel = () =>{
  againBillForm.dates = [];
  againFormVisible.value = false;
}
const loadDetailSumJd = (id, CompanName, AgentName) => {
    request({
      url: '/api/InputBillQuery/GetInputBillDetailSumJd',
      data: {
        searchKey: '',
        InputBillId: id,
        ProductName: '',
        ProductNo: '',
        CompanName: CompanName,
        AgentName: AgentName,
        StoreInItemCode: ''
      },
      method: 'post'
    }).then((res) => {
      sumJd.value = res.data.data;
    });
  };
  const getCachedMatchCondition = (id) => {
    request({
      url: `/api/MergeInputBill/getCachedMatchCondition/${id}`,
      method: 'get'
    }).then((res) => {
      console.log(res,'这是请求的数据=================================')
      if(res.data.code === 200){
        dateItems.startDate = res.data.data.matchCondition.startDate;
        dateItems.endDate = res.data.data.matchCondition.endDate;
      }
    });
  };
</script>
<style lang="scss" scoped>
.app-page-tabs {
  :deep(.el-tabs__content) {
    display: flex;
    overflow: hidden;
    .el-tab-pane {
      flex-direction: column;
      flex: 1;
      display: flex;
      overflow: hidden;
    }
  }
}
.app-page-header {
  :deep(.el-tabs__header){
    margin: 0 !important;
  }
  :deep(.el-tabs__nav-wrap::after){
    height: 0 !important;
  }
}
.table-box{
  display: flex;
  justify-content: space-between;
  .table-left{
    width: 39%;
    overflow: hidden;
  }
  .table-right{
    width: 59%;
  }
}
.bill-box{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
  .bill-left{
    margin: 0;
    font-size: 16px;
  }
}
.tips-box{
  color: #f00;
  font-size: 12px;
}
.cell-item{
  width: 50px;
}
.date-item{
  font-size: 12px;
  color: #f00;
  margin-left: 8px;
}
.bill-number{
  overflow-x: auto;
}
.bill-item{
  display: inline-block;
  width: 190px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
